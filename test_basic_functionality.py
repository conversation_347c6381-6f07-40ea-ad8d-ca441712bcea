#!/usr/bin/env python3
"""
Basic functionality test for Censor App without requiring all dependencies
"""

import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_imports():
    """Test that core modules can be imported"""
    print("Testing imports...")
    
    try:
        from config.settings import Settings, BodyPart, CensorMethod
        print("✓ Settings module imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import settings: {e}")
        return False
    
    try:
        # Test settings functionality
        settings = Settings("test_config.json")
        print("✓ Settings instance created successfully")
        
        # Test basic operations
        original_state = settings.enabled
        settings.toggle_censoring()
        new_state = settings.enabled
        assert new_state != original_state, "Toggle should change state"
        print("✓ Settings toggle functionality works")
        
        # Cleanup
        if os.path.exists("test_config.json"):
            os.remove("test_config.json")
        
    except Exception as e:
        print(f"✗ Settings functionality test failed: {e}")
        return False
    
    # Test screen capture import (may fail if dependencies not installed)
    try:
        from capture.screen_capture import ScreenCapture, Monitor, CaptureRegion
        print("✓ Screen capture module imported successfully")
    except ImportError as e:
        print(f"⚠ Screen capture import failed (dependencies not installed): {e}")
    
    # Test image utils import
    try:
        from utils.image_utils import ImageProcessor
        print("✓ Image utils module imported successfully")
    except ImportError as e:
        print(f"⚠ Image utils import failed (dependencies not installed): {e}")

    # Test detection engine import
    try:
        from detection.nudity_detector import NudityDetector, Detection, DetectionResult
        print("✓ Detection engine module imported successfully")

        # Test basic detector creation (without model loading)
        detector = NudityDetector()
        print("✓ Detection engine instance created successfully")

        # Test configuration
        detector.set_confidence_threshold(0.8)
        assert detector.confidence_threshold == 0.8, "Confidence threshold setting failed"
        print("✓ Detection engine configuration works")

    except ImportError as e:
        print(f"⚠ Detection engine import failed (dependencies not installed): {e}")
    except Exception as e:
        print(f"⚠ Detection engine test failed: {e}")

    # Test overlay renderer import
    try:
        from overlay.overlay_renderer import OverlayRenderer, OverlayRegion
        print("✓ Overlay renderer module imported successfully")

        # Test basic renderer creation (without GUI)
        renderer = OverlayRenderer()
        print("✓ Overlay renderer instance created successfully")

        # Test configuration
        renderer.set_enabled(False)
        assert not renderer.enabled, "Overlay enable/disable failed"
        renderer.set_enabled(True)
        assert renderer.enabled, "Overlay enable/disable failed"
        print("✓ Overlay renderer configuration works")

        # Test status reporting
        status = renderer.get_status()
        assert isinstance(status, dict), "Status should be a dictionary"
        assert 'enabled' in status, "Status should include enabled field"
        print("✓ Overlay renderer status reporting works")

    except ImportError as e:
        print(f"⚠ Overlay renderer import failed (dependencies not installed): {e}")
    except Exception as e:
        print(f"⚠ Overlay renderer test failed: {e}")

    return True

def test_project_structure():
    """Test that project structure is correct"""
    print("\nTesting project structure...")
    
    required_files = [
        "main.py",
        "requirements.txt",
        "setup.py",
        "README.md",
        "src/__init__.py",
        "src/config/__init__.py",
        "src/config/settings.py",
        "src/app_controller.py",
        "src/capture/__init__.py",
        "src/capture/screen_capture.py",
        "src/detection/__init__.py",
        "src/overlay/__init__.py",
        "src/gui/__init__.py",
        "src/gui/system_tray.py",
        "src/gui/main_window.py",
        "src/utils/__init__.py",
        "src/utils/image_utils.py",
        "tests/test_basic.py",
        "tests/test_screen_capture.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print(f"✗ Missing files: {missing_files}")
        return False
    else:
        print("✓ All required files present")
        return True

def test_configuration():
    """Test configuration system"""
    print("\nTesting configuration system...")
    
    try:
        from config.settings import Settings, BodyPart, CensorMethod
        
        # Create settings instance
        settings = Settings("test_config_2.json")
        
        # Test default values
        assert not settings.enabled, "Should be disabled by default"
        assert settings.detection.confidence_threshold == 0.7, "Default confidence should be 0.7"
        assert settings.performance.max_fps == 30, "Default FPS should be 30"
        
        # Test body part settings
        breasts_setting = settings.get_body_part_setting(BodyPart.BREASTS)
        assert breasts_setting.enabled, "Breasts censoring should be enabled by default"
        assert breasts_setting.method == CensorMethod.BLUR, "Default method should be blur"
        
        # Test save/load
        settings.enabled = True
        settings.detection.confidence_threshold = 0.9
        settings.save()
        
        # Load in new instance
        new_settings = Settings("test_config_2.json")
        assert new_settings.enabled, "Enabled state should be saved"
        assert new_settings.detection.confidence_threshold == 0.9, "Confidence should be saved"
        
        # Cleanup
        if os.path.exists("test_config_2.json"):
            os.remove("test_config_2.json")
        
        print("✓ Configuration system works correctly")
        return True
        
    except Exception as e:
        print(f"✗ Configuration test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("Censor App - Basic Functionality Test")
    print("=" * 40)
    
    tests = [
        test_project_structure,
        test_imports,
        test_configuration
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ Test {test.__name__} failed with exception: {e}")
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("✓ All basic functionality tests passed!")
        print("\nNext steps:")
        print("1. Run 'python setup.py' to install dependencies")
        print("2. Run 'python main.py' to start the application")
        return True
    else:
        print("✗ Some tests failed. Please check the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
