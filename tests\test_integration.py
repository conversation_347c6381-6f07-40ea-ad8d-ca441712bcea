"""
Comprehensive Integration Tests for Censor App
Tests end-to-end workflows and component integration
"""

import unittest
import sys
import os
import time
import threading
import tempfile
import shutil
from unittest.mock import Mock, patch, MagicMock

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from src.config.settings import Settings, BodyPart, CensorMethod
from src.capture.screen_capture import ScreenCapture, MonitorInfo
from src.detection.nudity_detector import NudityDetector, Detection, DetectionResult
from src.overlay.overlay_renderer import OverlayRenderer
from src.performance.performance_manager import PerformanceManager

class TestEndToEndWorkflow(unittest.TestCase):
    """Test complete end-to-end censoring workflow"""
    
    def setUp(self):
        """Set up test environment"""
        self.temp_dir = tempfile.mkdtemp()
        self.config_file = os.path.join(self.temp_dir, "test_config.json")
        
        # Initialize components
        self.settings = Settings(self.config_file)
        self.screen_capture = None
        self.detector = None
        self.overlay_renderer = None
        self.performance_manager = None
        
    def tearDown(self):
        """Clean up test environment"""
        if self.screen_capture:
            self.screen_capture.stop_capture()
        if self.overlay_renderer:
            self.overlay_renderer.clear_overlays()
        
        # Clean up temp directory
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_component_initialization_workflow(self):
        """Test complete component initialization workflow"""
        try:
            # Initialize screen capture
            self.screen_capture = ScreenCapture()
            monitors = self.screen_capture.get_monitors()
            self.assertGreater(len(monitors), 0, "Should detect at least one monitor")
            
            # Initialize detector
            self.detector = NudityDetector()
            self.assertIsNotNone(self.detector)
            
            # Initialize overlay renderer
            self.overlay_renderer = OverlayRenderer()
            self.assertIsNotNone(self.overlay_renderer)
            
            # Initialize performance manager
            self.performance_manager = PerformanceManager()
            self.assertIsNotNone(self.performance_manager)
            
            print("✓ All components initialized successfully")
            
        except Exception as e:
            self.fail(f"Component initialization failed: {e}")
    
    def test_capture_detect_overlay_pipeline(self):
        """Test the complete capture -> detect -> overlay pipeline"""
        try:
            # Initialize components
            self.screen_capture = ScreenCapture()
            self.detector = NudityDetector()
            self.overlay_renderer = OverlayRenderer()
            
            # Setup monitors for overlay
            monitors = self.screen_capture.get_monitors()
            success = self.overlay_renderer.setup_monitors_from_monitor_info(monitors)
            self.assertTrue(success, "Overlay monitor setup should succeed")
            
            # Capture a frame
            frame = self.screen_capture.capture_frame()
            if frame is not None:
                self.assertIsNotNone(frame)
                self.assertEqual(len(frame.shape), 3)  # Should be RGB
                
                # Create mock detection result for testing
                mock_detections = [
                    Detection(
                        body_part=BodyPart.BREASTS,
                        confidence=0.85,
                        bbox=(0.2, 0.3, 0.4, 0.5),
                        raw_class="FEMALE_BREAST_EXPOSED"
                    )
                ]
                
                # Test overlay update
                self.overlay_renderer.update_overlays(
                    detections=mock_detections,
                    monitor_index=0,
                    frame_shape=frame.shape[:2]
                )
                
                print("✓ Capture -> Detect -> Overlay pipeline test passed")
            else:
                print("! Frame capture returned None (expected in test environment)")
                
        except Exception as e:
            self.fail(f"Pipeline test failed: {e}")
    
    def test_settings_integration_workflow(self):
        """Test settings integration with all components"""
        try:
            # Modify settings
            self.settings.enabled = True
            self.settings.detection.confidence_threshold = 0.8
            self.settings.performance.max_fps = 60
            
            # Test body part settings
            breasts_setting = self.settings.get_body_part_setting(BodyPart.BREASTS)
            breasts_setting.enabled = True
            breasts_setting.method = CensorMethod.PIXELATION
            breasts_setting.intensity = 0.9
            
            # Save settings
            self.settings.save()
            
            # Load settings in new instance
            new_settings = Settings(self.config_file)
            self.assertTrue(new_settings.enabled)
            self.assertEqual(new_settings.detection.confidence_threshold, 0.8)
            self.assertEqual(new_settings.performance.max_fps, 60)
            
            new_breasts_setting = new_settings.get_body_part_setting(BodyPart.BREASTS)
            self.assertTrue(new_breasts_setting.enabled)
            self.assertEqual(new_breasts_setting.method, CensorMethod.PIXELATION)
            self.assertEqual(new_breasts_setting.intensity, 0.9)
            
            print("✓ Settings integration workflow test passed")
            
        except Exception as e:
            self.fail(f"Settings integration test failed: {e}")
    
    def test_multi_monitor_integration(self):
        """Test multi-monitor integration workflow"""
        try:
            self.screen_capture = ScreenCapture()
            self.overlay_renderer = OverlayRenderer()
            
            monitors = self.screen_capture.get_monitors()
            
            if len(monitors) > 1:
                # Test multi-monitor setup
                success = self.overlay_renderer.setup_monitors_from_monitor_info(monitors)
                self.assertTrue(success)
                
                # Test capture from different monitors
                for i, monitor in enumerate(monitors):
                    frame = self.screen_capture.capture_frame(monitor_index=i)
                    if frame is not None:
                        self.assertIsNotNone(frame)
                        print(f"✓ Monitor {i} capture successful: {frame.shape}")
                
                print(f"✓ Multi-monitor integration test passed ({len(monitors)} monitors)")
            else:
                print("! Single monitor detected - multi-monitor test skipped")
                
        except Exception as e:
            self.fail(f"Multi-monitor integration test failed: {e}")
    
    def test_performance_integration(self):
        """Test performance manager integration"""
        try:
            self.screen_capture = ScreenCapture()
            self.performance_manager = PerformanceManager()
            
            # Enable performance optimization
            self.performance_manager.enable_frame_skipping(True)
            self.performance_manager.enable_roi_detection(True)
            self.performance_manager.enable_detection_caching(True)
            
            # Test performance stats collection
            stats = self.performance_manager.get_performance_stats()
            self.assertIsInstance(stats, dict)
            self.assertIn('frame_skipping_enabled', stats)
            self.assertIn('roi_detection_enabled', stats)
            self.assertIn('detection_caching_enabled', stats)
            
            # Test resource monitoring
            resource_stats = self.performance_manager.get_resource_usage()
            self.assertIsInstance(resource_stats, dict)
            self.assertIn('cpu_percent', resource_stats)
            self.assertIn('memory_mb', resource_stats)
            
            print("✓ Performance integration test passed")
            
        except Exception as e:
            self.fail(f"Performance integration test failed: {e}")

class TestThreadingIntegration(unittest.TestCase):
    """Test multi-threading integration and coordination"""
    
    def setUp(self):
        """Set up test environment"""
        self.components_initialized = False
        self.test_results = {}
    
    def test_concurrent_component_access(self):
        """Test concurrent access to components"""
        try:
            screen_capture = ScreenCapture()
            detector = NudityDetector()
            
            def capture_worker():
                """Worker function for capture testing"""
                try:
                    for i in range(5):
                        frame = screen_capture.capture_frame()
                        time.sleep(0.1)
                    self.test_results['capture'] = True
                except Exception as e:
                    self.test_results['capture'] = f"Error: {e}"
            
            def detection_worker():
                """Worker function for detection testing"""
                try:
                    for i in range(5):
                        stats = detector.get_performance_stats()
                        self.assertIsInstance(stats, dict)
                        time.sleep(0.1)
                    self.test_results['detection'] = True
                except Exception as e:
                    self.test_results['detection'] = f"Error: {e}"
            
            # Start concurrent threads
            capture_thread = threading.Thread(target=capture_worker)
            detection_thread = threading.Thread(target=detection_worker)
            
            capture_thread.start()
            detection_thread.start()
            
            # Wait for completion
            capture_thread.join(timeout=10)
            detection_thread.join(timeout=10)
            
            # Check results
            self.assertTrue(self.test_results.get('capture', False))
            self.assertTrue(self.test_results.get('detection', False))
            
            print("✓ Concurrent component access test passed")
            
        except Exception as e:
            self.fail(f"Threading integration test failed: {e}")

class TestErrorHandlingIntegration(unittest.TestCase):
    """Test error handling and recovery in integrated scenarios"""
    
    def test_component_failure_recovery(self):
        """Test recovery from component failures"""
        try:
            # Test screen capture with invalid monitor
            screen_capture = ScreenCapture()
            
            # Try to capture from non-existent monitor
            frame = screen_capture.capture_frame(monitor_index=999)
            # Should handle gracefully (return None or raise handled exception)
            
            # Test detector with invalid input
            detector = NudityDetector()
            
            # Try detection with None frame
            result = detector.detect_nudity(None)
            # Should handle gracefully
            
            print("✓ Component failure recovery test passed")
            
        except Exception as e:
            # Expected behavior - components should handle errors gracefully
            print(f"✓ Component failure recovery test passed (handled exception: {type(e).__name__})")
    
    def test_resource_exhaustion_handling(self):
        """Test handling of resource exhaustion scenarios"""
        try:
            performance_manager = PerformanceManager()
            
            # Test memory monitoring
            resource_stats = performance_manager.get_resource_usage()
            initial_memory = resource_stats.get('memory_mb', 0)
            
            # Simulate high resource usage detection
            if initial_memory > 0:
                print(f"✓ Resource monitoring working (Memory: {initial_memory:.1f} MB)")
            
            print("✓ Resource exhaustion handling test passed")
            
        except Exception as e:
            self.fail(f"Resource exhaustion test failed: {e}")

def run_integration_tests():
    """Run all integration tests"""
    print("=" * 60)
    print("INTEGRATION TESTS")
    print("=" * 60)
    
    # Create test suite
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Add test classes
    suite.addTests(loader.loadTestsFromTestCase(TestEndToEndWorkflow))
    suite.addTests(loader.loadTestsFromTestCase(TestThreadingIntegration))
    suite.addTests(loader.loadTestsFromTestCase(TestErrorHandlingIntegration))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    print("\n" + "=" * 60)
    if result.wasSuccessful():
        print("✓ ALL INTEGRATION TESTS PASSED!")
    else:
        print("✗ Some integration tests failed")
        for failure in result.failures:
            print(f"FAILURE: {failure[0]}")
        for error in result.errors:
            print(f"ERROR: {error[0]}")
    
    print("=" * 60)
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_integration_tests()
    sys.exit(0 if success else 1)
