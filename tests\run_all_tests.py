"""
Comprehensive Test Runner for Censor App
Runs all test suites and provides detailed reporting
"""

import sys
import os
import time
import platform
import subprocess
from datetime import datetime

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

# Import test runners
from test_integration import run_integration_tests
from test_windows11_compatibility import run_windows11_compatibility_tests
from test_detection_accuracy import run_detection_accuracy_tests
from test_error_handling import run_error_handling_tests
from test_performance_benchmarks import run_performance_benchmarks
from test_system_integration import run_system_integration_tests

class TestReport:
    """Test report generator"""
    
    def __init__(self):
        self.start_time = time.time()
        self.test_results = {}
        self.system_info = self._get_system_info()
        
    def _get_system_info(self):
        """Get comprehensive system information"""
        info = {
            'platform': platform.system(),
            'platform_version': platform.version(),
            'platform_release': platform.release(),
            'processor': platform.processor(),
            'python_version': platform.python_version(),
            'test_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        }
        
        try:
            import psutil
            info.update({
                'cpu_count': psutil.cpu_count(),
                'memory_gb': round(psutil.virtual_memory().total / (1024**3), 1),
                'cpu_freq_mhz': round(psutil.cpu_freq().current) if psutil.cpu_freq() else 'Unknown'
            })
        except ImportError:
            info.update({
                'cpu_count': 'Unknown',
                'memory_gb': 'Unknown',
                'cpu_freq_mhz': 'Unknown'
            })
        
        return info
    
    def add_test_result(self, test_name, success, duration, details=None):
        """Add a test result to the report"""
        self.test_results[test_name] = {
            'success': success,
            'duration': duration,
            'details': details or {}
        }
    
    def generate_report(self):
        """Generate comprehensive test report"""
        total_time = time.time() - self.start_time
        
        print("\n" + "=" * 80)
        print("COMPREHENSIVE TEST REPORT")
        print("=" * 80)
        
        # System Information
        print("\nSYSTEM INFORMATION:")
        print("-" * 40)
        for key, value in self.system_info.items():
            print(f"{key.replace('_', ' ').title()}: {value}")
        
        # Test Results Summary
        print(f"\nTEST RESULTS SUMMARY:")
        print("-" * 40)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result['success'])
        failed_tests = total_tests - passed_tests
        
        print(f"Total Test Suites: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {failed_tests}")
        print(f"Success Rate: {(passed_tests/total_tests*100):.1f}%" if total_tests > 0 else "N/A")
        print(f"Total Duration: {total_time:.1f} seconds")
        
        # Detailed Results
        print(f"\nDETAILED RESULTS:")
        print("-" * 40)
        
        for test_name, result in self.test_results.items():
            status = "✓ PASS" if result['success'] else "✗ FAIL"
            duration = result['duration']
            print(f"{status} {test_name:<30} ({duration:.1f}s)")
            
            # Show details if available
            if result['details']:
                for key, value in result['details'].items():
                    print(f"    {key}: {value}")
        
        # Overall Status
        print("\n" + "=" * 80)
        if failed_tests == 0:
            print("🎉 ALL TESTS PASSED! The Censor App is ready for deployment.")
        else:
            print(f"⚠️  {failed_tests} TEST SUITE(S) FAILED. Review failures before deployment.")
        print("=" * 80)
        
        return failed_tests == 0

def check_dependencies():
    """Check if required dependencies are available"""
    print("Checking dependencies...")
    
    dependencies = {
        'numpy': 'numpy',
        'opencv': 'cv2',
        'mss': 'mss',
        'psutil': 'psutil',
        'PyQt6': 'PyQt6',
    }
    
    missing = []
    available = []
    
    for name, import_name in dependencies.items():
        try:
            __import__(import_name)
            available.append(name)
        except ImportError:
            missing.append(name)
    
    print(f"✓ Available: {', '.join(available)}")
    if missing:
        print(f"! Missing: {', '.join(missing)}")
        print("  Some tests may be skipped due to missing dependencies.")
    
    return len(missing) == 0

def run_individual_test_suite(test_name, test_function, report):
    """Run an individual test suite and record results"""
    print(f"\n{'='*20} STARTING {test_name.upper()} {'='*20}")
    
    start_time = time.time()
    try:
        success = test_function()
        duration = time.time() - start_time
        report.add_test_result(test_name, success, duration)
        
        status = "COMPLETED" if success else "FAILED"
        print(f"{'='*20} {test_name.upper()} {status} ({duration:.1f}s) {'='*20}")
        
        return success
    except Exception as e:
        duration = time.time() - start_time
        report.add_test_result(test_name, False, duration, {'error': str(e)})
        
        print(f"{'='*20} {test_name.upper()} ERROR ({duration:.1f}s) {'='*20}")
        print(f"Error: {e}")
        
        return False

def run_basic_tests():
    """Run basic unit tests using unittest discovery"""
    print("Running basic unit tests...")
    
    try:
        # Run existing unit tests
        import unittest
        
        # Discover and run tests
        loader = unittest.TestLoader()
        start_dir = os.path.dirname(__file__)
        
        # Load specific test files
        test_files = [
            'test_basic.py',
            'test_detection.py',
            'test_screen_capture.py',
            'test_overlay.py',
            'test_hotkeys.py',
            'test_performance_optimization.py',
            'test_multi_monitor_support.py'
        ]
        
        suite = unittest.TestSuite()
        
        for test_file in test_files:
            test_path = os.path.join(start_dir, test_file)
            if os.path.exists(test_path):
                try:
                    # Import the test module
                    module_name = test_file[:-3]  # Remove .py
                    spec = __import__(module_name)
                    
                    # Load tests from module
                    module_suite = loader.loadTestsFromModule(spec)
                    suite.addTest(module_suite)
                except Exception as e:
                    print(f"! Failed to load {test_file}: {e}")
        
        # Run the tests
        runner = unittest.TextTestRunner(verbosity=1)
        result = runner.run(suite)
        
        return result.wasSuccessful()
        
    except Exception as e:
        print(f"! Basic tests error: {e}")
        return False

def main():
    """Main test runner function"""
    print("🧪 CENSOR APP - COMPREHENSIVE TEST SUITE")
    print("=" * 80)
    print(f"Starting comprehensive testing at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Initialize report
    report = TestReport()
    
    # Check dependencies
    all_deps_available = check_dependencies()
    if not all_deps_available:
        print("\n⚠️  Some dependencies are missing. Tests will continue but some may be skipped.")
    
    # Define test suites to run
    test_suites = [
        ('Basic Unit Tests', run_basic_tests),
        ('Integration Tests', run_integration_tests),
        ('System Integration', run_system_integration_tests),
        ('Windows 11 Compatibility', run_windows11_compatibility_tests),
        ('Detection Accuracy', run_detection_accuracy_tests),
        ('Error Handling', run_error_handling_tests),
        ('Performance Benchmarks', run_performance_benchmarks),
    ]
    
    # Run all test suites
    all_passed = True
    
    for test_name, test_function in test_suites:
        success = run_individual_test_suite(test_name, test_function, report)
        if not success:
            all_passed = False
    
    # Generate final report
    final_success = report.generate_report()
    
    # Return appropriate exit code
    return 0 if final_success else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
