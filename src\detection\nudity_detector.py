"""
Nudity Detection Engine for Censor App
Uses NudeNet for AI-powered nudity detection
"""

import logging
import time
import os
from typing import List, Dict, Optional, Tuple, Any
import numpy as np
from dataclasses import dataclass
from enum import Enum

# Import detection libraries (will be available after setup)
try:
    from nudenet import NudeDetector
    import cv2
    from PIL import Image
    DETECTION_AVAILABLE = True
except ImportError:
    DETECTION_AVAILABLE = False

from src.config.settings import BodyPart

@dataclass
class Detection:
    """Single detection result"""
    body_part: BodyPart
    confidence: float
    bbox: Tuple[float, float, float, float]  # x1, y1, x2, y2 (normalized 0-1)
    raw_class: str  # Original NudeNet class name

@dataclass
class DetectionResult:
    """Complete detection result for a frame"""
    detections: List[Detection]
    processing_time: float
    frame_shape: Tuple[int, int]  # height, width
    timestamp: float

class NudityDetector:
    """AI-powered nudity detection using NudeNet"""
    
    # Mapping from NudeNet classes to our BodyPart enum
    CLASS_MAPPING = {
        'FEMALE_BREAST_EXPOSED': BodyPart.BREASTS,
        'MALE_BREAST_EXPOSED': BodyPart.BREASTS,
        'FEMALE_GENITALIA_EXPOSED': BodyPart.GENITALS,
        'MALE_GENITALIA_EXPOSED': BodyPart.GENITALS,
        'BUTTOCKS_EXPOSED': BodyPart.BUTTOCKS,
        'ANUS_EXPOSED': BodyPart.BUTTOCKS,
        # Additional mappings for different model versions
        'BREAST_F': BodyPart.BREASTS,
        'BREAST_M': BodyPart.BREASTS,
        'GENITALIA_F': BodyPart.GENITALS,
        'GENITALIA_M': BodyPart.GENITALS,
        'BUTTOCKS': BodyPart.BUTTOCKS,
    }
    
    def __init__(self, model_path: Optional[str] = None):
        self.logger = logging.getLogger(__name__)
        
        # Model configuration
        self.model_path = model_path
        self.model: Optional[Any] = None
        self.model_loaded = False
        
        # Performance settings
        self.confidence_threshold = 0.7
        self.max_detections = 50
        self.input_size = (320, 320)  # Smaller size for better performance
        
        # Performance tracking
        self.detection_times = []
        self.total_detections = 0
        self.total_frames = 0
        
        # Check if detection is available
        if not DETECTION_AVAILABLE:
            self.logger.warning("Detection libraries not available. Install dependencies first.")
        
        self.logger.info("Nudity detector initialized")
    
    def load_model(self) -> bool:
        """Load the NudeNet model"""
        if not DETECTION_AVAILABLE:
            self.logger.error("Cannot load model: detection libraries not available")
            return False
        
        if self.model_loaded:
            self.logger.info("Model already loaded")
            return True
        
        try:
            self.logger.info("Loading NudeNet model...")
            start_time = time.time()
            
            # Initialize NudeNet detector
            # The model will be downloaded automatically on first use
            self.model = NudeDetector()
            
            load_time = time.time() - start_time
            self.model_loaded = True
            
            self.logger.info(f"Model loaded successfully in {load_time:.2f} seconds")
            return True
            
        except Exception as e:
            self.logger.error(f"Error loading model: {e}")
            self.model_loaded = False
            return False
    
    def set_confidence_threshold(self, threshold: float):
        """Set confidence threshold for detections"""
        if 0.0 <= threshold <= 1.0:
            self.confidence_threshold = threshold
            self.logger.info(f"Confidence threshold set to {threshold}")
        else:
            self.logger.error(f"Invalid confidence threshold: {threshold}")
    
    def set_input_size(self, width: int, height: int):
        """Set input size for model inference"""
        if width > 0 and height > 0:
            self.input_size = (width, height)
            self.logger.info(f"Input size set to {width}x{height}")
        else:
            self.logger.error(f"Invalid input size: {width}x{height}")
    
    def preprocess_frame(self, frame: np.ndarray) -> np.ndarray:
        """Preprocess frame for detection"""
        try:
            # Convert BGR to RGB if needed
            if len(frame.shape) == 3 and frame.shape[2] == 3:
                # Assume BGR from OpenCV, convert to RGB
                frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            else:
                frame_rgb = frame
            
            # Resize for faster inference while maintaining aspect ratio
            original_height, original_width = frame_rgb.shape[:2]
            target_width, target_height = self.input_size
            
            # Calculate scaling to fit within target size
            scale_w = target_width / original_width
            scale_h = target_height / original_height
            scale = min(scale_w, scale_h)
            
            new_width = int(original_width * scale)
            new_height = int(original_height * scale)
            
            # Resize frame
            resized = cv2.resize(frame_rgb, (new_width, new_height), interpolation=cv2.INTER_LANCZOS4)
            
            return resized
            
        except Exception as e:
            self.logger.error(f"Error preprocessing frame: {e}")
            return frame
    
    def detect_nudity(self, frame: np.ndarray) -> DetectionResult:
        """Detect nudity in a frame"""
        start_time = time.time()
        
        if not self.model_loaded:
            if not self.load_model():
                return DetectionResult([], 0.0, frame.shape[:2], time.time())
        
        try:
            # Preprocess frame
            processed_frame = self.preprocess_frame(frame)
            
            # Run detection
            raw_detections = self.model.detect(processed_frame)
            
            # Process results
            detections = self._process_detections(raw_detections, frame.shape[:2], processed_frame.shape[:2])
            
            # Update performance tracking
            processing_time = time.time() - start_time
            self.detection_times.append(processing_time)
            if len(self.detection_times) > 100:  # Keep last 100 measurements
                self.detection_times.pop(0)
            
            self.total_frames += 1
            self.total_detections += len(detections)
            
            return DetectionResult(
                detections=detections,
                processing_time=processing_time,
                frame_shape=frame.shape[:2],
                timestamp=time.time()
            )
            
        except Exception as e:
            self.logger.error(f"Error during detection: {e}")
            return DetectionResult([], time.time() - start_time, frame.shape[:2], time.time())
    
    def _process_detections(self, raw_detections: List[Dict], 
                          original_shape: Tuple[int, int],
                          processed_shape: Tuple[int, int]) -> List[Detection]:
        """Process raw NudeNet detections into our format"""
        detections = []
        
        try:
            original_height, original_width = original_shape
            processed_height, processed_width = processed_shape
            
            # Calculate scaling factors to convert back to original coordinates
            scale_x = original_width / processed_width
            scale_y = original_height / processed_height
            
            for raw_det in raw_detections:
                # Extract detection info
                class_name = raw_det.get('class', '')
                confidence = raw_det.get('score', 0.0)
                bbox = raw_det.get('box', [0, 0, 0, 0])
                
                # Filter by confidence
                if confidence < self.confidence_threshold:
                    continue
                
                # Map class to body part
                body_part = self.CLASS_MAPPING.get(class_name.upper())
                if body_part is None:
                    self.logger.debug(f"Unknown class: {class_name}")
                    continue
                
                # Convert bbox to normalized coordinates
                x1, y1, x2, y2 = bbox
                
                # Scale back to original frame coordinates
                x1 = x1 * scale_x
                y1 = y1 * scale_y
                x2 = x2 * scale_x
                y2 = y2 * scale_y
                
                # Normalize to 0-1 range
                norm_x1 = max(0, min(1, x1 / original_width))
                norm_y1 = max(0, min(1, y1 / original_height))
                norm_x2 = max(0, min(1, x2 / original_width))
                norm_y2 = max(0, min(1, y2 / original_height))
                
                # Create detection
                detection = Detection(
                    body_part=body_part,
                    confidence=confidence,
                    bbox=(norm_x1, norm_y1, norm_x2, norm_y2),
                    raw_class=class_name
                )
                
                detections.append(detection)
            
            # Sort by confidence (highest first)
            detections.sort(key=lambda d: d.confidence, reverse=True)
            
            # Limit number of detections
            if len(detections) > self.max_detections:
                detections = detections[:self.max_detections]
            
            return detections
            
        except Exception as e:
            self.logger.error(f"Error processing detections: {e}")
            return []
    
    def get_performance_stats(self) -> Dict:
        """Get detection performance statistics"""
        avg_detection_time = 0
        if self.detection_times:
            avg_detection_time = sum(self.detection_times) / len(self.detection_times)
        
        avg_detections_per_frame = 0
        if self.total_frames > 0:
            avg_detections_per_frame = self.total_detections / self.total_frames
        
        return {
            "model_loaded": self.model_loaded,
            "total_frames": self.total_frames,
            "total_detections": self.total_detections,
            "avg_detection_time_ms": avg_detection_time * 1000,
            "avg_detections_per_frame": avg_detections_per_frame,
            "confidence_threshold": self.confidence_threshold,
            "input_size": self.input_size,
            "detection_available": DETECTION_AVAILABLE
        }
    
    def filter_detections_by_body_parts(self, detections: List[Detection], 
                                      enabled_parts: List[BodyPart]) -> List[Detection]:
        """Filter detections based on enabled body parts"""
        return [det for det in detections if det.body_part in enabled_parts]
    
    def __del__(self):
        """Cleanup when detector is destroyed"""
        try:
            if hasattr(self, 'model') and self.model is not None:
                # NudeNet doesn't require explicit cleanup, but we can clear the reference
                self.model = None
        except:
            pass
