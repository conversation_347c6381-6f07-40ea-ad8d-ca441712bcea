"""
Detection Accuracy Tests for Censor App
Tests nudity detection accuracy with various content types and scenarios
"""

import unittest
import sys
import os
import numpy as np
import time
from unittest.mock import Mock, patch

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from src.detection.nudity_detector import NudityDetector, Detection, DetectionResult
from src.config.settings import BodyPart

class TestDetectionAccuracy(unittest.TestCase):
    """Test detection accuracy with various scenarios"""
    
    def setUp(self):
        """Set up test environment"""
        self.detector = NudityDetector()
        self.model_available = False
        
        # Try to load the model for accuracy testing
        try:
            import nudenet
            self.model_available = self.detector.load_model()
            if self.model_available:
                print("✓ NudeNet model loaded for accuracy testing")
            else:
                print("! NudeNet model not available - using mock tests")
        except ImportError:
            print("! NudeNet not installed - using mock tests")
    
    def _create_test_image(self, width=640, height=480, pattern='random'):
        """Create test images with different patterns"""
        if pattern == 'random':
            return np.random.randint(0, 255, (height, width, 3), dtype=np.uint8)
        elif pattern == 'solid_red':
            image = np.zeros((height, width, 3), dtype=np.uint8)
            image[:, :, 0] = 255  # Red channel
            return image
        elif pattern == 'gradient':
            image = np.zeros((height, width, 3), dtype=np.uint8)
            for i in range(height):
                image[i, :, :] = int(255 * i / height)
            return image
        elif pattern == 'checkerboard':
            image = np.zeros((height, width, 3), dtype=np.uint8)
            for i in range(height):
                for j in range(width):
                    if (i // 32 + j // 32) % 2:
                        image[i, j, :] = 255
            return image
        else:
            return np.random.randint(0, 255, (height, width, 3), dtype=np.uint8)
    
    def test_detection_with_various_image_types(self):
        """Test detection with various image types and patterns"""
        test_patterns = ['random', 'solid_red', 'gradient', 'checkerboard']
        
        for pattern in test_patterns:
            with self.subTest(pattern=pattern):
                test_image = self._create_test_image(pattern=pattern)
                
                if self.model_available:
                    # Test with actual model
                    result = self.detector.detect_nudity(test_image)
                    self.assertIsInstance(result, DetectionResult)
                    self.assertIsInstance(result.detections, list)
                    self.assertGreater(result.processing_time, 0)
                    print(f"✓ {pattern} pattern: {len(result.detections)} detections, {result.processing_time*1000:.1f}ms")
                else:
                    # Test preprocessing without model
                    processed = self.detector.preprocess_frame(test_image)
                    self.assertIsInstance(processed, np.ndarray)
                    print(f"✓ {pattern} pattern: preprocessing successful")
    
    def test_detection_with_different_resolutions(self):
        """Test detection accuracy with different image resolutions"""
        resolutions = [
            (320, 240),   # Low resolution
            (640, 480),   # Standard resolution
            (1280, 720),  # HD resolution
            (1920, 1080), # Full HD resolution
        ]
        
        for width, height in resolutions:
            with self.subTest(resolution=f"{width}x{height}"):
                test_image = self._create_test_image(width, height)
                
                if self.model_available:
                    start_time = time.time()
                    result = self.detector.detect_nudity(test_image)
                    processing_time = time.time() - start_time
                    
                    self.assertIsInstance(result, DetectionResult)
                    self.assertEqual(result.frame_shape, (height, width))
                    print(f"✓ {width}x{height}: {len(result.detections)} detections, {processing_time*1000:.1f}ms")
                else:
                    # Test preprocessing
                    processed = self.detector.preprocess_frame(test_image)
                    self.assertIsInstance(processed, np.ndarray)
                    print(f"✓ {width}x{height}: preprocessing successful")
    
    def test_confidence_threshold_effects(self):
        """Test how confidence threshold affects detection results"""
        test_image = self._create_test_image()
        thresholds = [0.3, 0.5, 0.7, 0.9]
        
        if self.model_available:
            detection_counts = []
            
            for threshold in thresholds:
                self.detector.set_confidence_threshold(threshold)
                result = self.detector.detect_nudity(test_image)
                detection_count = len(result.detections)
                detection_counts.append(detection_count)
                print(f"✓ Threshold {threshold}: {detection_count} detections")
            
            # Generally, higher thresholds should result in fewer detections
            # (though this may not always be true with random test images)
            print(f"Detection counts by threshold: {dict(zip(thresholds, detection_counts))}")
        else:
            # Test threshold setting without model
            for threshold in thresholds:
                self.detector.set_confidence_threshold(threshold)
                self.assertEqual(self.detector.confidence_threshold, threshold)
                print(f"✓ Threshold {threshold}: setting successful")
    
    def test_body_part_classification_accuracy(self):
        """Test body part classification accuracy"""
        if not self.model_available:
            self.skipTest("Model not available for classification testing")
        
        test_image = self._create_test_image()
        result = self.detector.detect_nudity(test_image)
        
        # Check that all detected body parts are valid
        valid_body_parts = set(BodyPart)
        for detection in result.detections:
            self.assertIn(detection.body_part, valid_body_parts)
            self.assertIsInstance(detection.confidence, float)
            self.assertGreaterEqual(detection.confidence, 0.0)
            self.assertLessEqual(detection.confidence, 1.0)
            print(f"✓ Detection: {detection.body_part.value} (confidence: {detection.confidence:.3f})")
    
    def test_detection_consistency(self):
        """Test detection consistency across multiple runs"""
        if not self.model_available:
            self.skipTest("Model not available for consistency testing")
        
        test_image = self._create_test_image()
        results = []
        
        # Run detection multiple times
        for i in range(5):
            result = self.detector.detect_nudity(test_image)
            results.append(len(result.detections))
        
        # Check consistency (results should be identical for same input)
        if len(set(results)) == 1:
            print(f"✓ Detection consistency: {results[0]} detections across all runs")
        else:
            print(f"! Detection variance detected: {results}")
            # Some variance might be acceptable depending on model implementation
    
    def test_false_positive_scenarios(self):
        """Test scenarios that might produce false positives"""
        false_positive_scenarios = [
            ('solid_red', 'Solid red image'),
            ('gradient', 'Gradient image'),
            ('checkerboard', 'Checkerboard pattern'),
        ]
        
        for pattern, description in false_positive_scenarios:
            with self.subTest(scenario=description):
                test_image = self._create_test_image(pattern=pattern)
                
                if self.model_available:
                    result = self.detector.detect_nudity(test_image)
                    detection_count = len(result.detections)
                    
                    # For artificial patterns, we generally expect few or no detections
                    print(f"✓ {description}: {detection_count} detections")
                    
                    # Log any high-confidence detections for review
                    high_conf_detections = [d for d in result.detections if d.confidence > 0.8]
                    if high_conf_detections:
                        print(f"  ! High confidence detections: {len(high_conf_detections)}")
                else:
                    print(f"✓ {description}: preprocessing test passed")

class TestDetectionPerformance(unittest.TestCase):
    """Test detection performance characteristics"""
    
    def setUp(self):
        """Set up test environment"""
        self.detector = NudityDetector()
        self.model_available = False
        
        try:
            import nudenet
            self.model_available = self.detector.load_model()
        except ImportError:
            pass
    
    def test_detection_speed_benchmarks(self):
        """Test detection speed across different scenarios"""
        if not self.model_available:
            self.skipTest("Model not available for performance testing")
        
        test_scenarios = [
            (320, 240, "Low resolution"),
            (640, 480, "Standard resolution"),
            (1280, 720, "HD resolution"),
        ]
        
        for width, height, description in test_scenarios:
            with self.subTest(scenario=description):
                test_image = np.random.randint(0, 255, (height, width, 3), dtype=np.uint8)
                
                # Warm up
                self.detector.detect_nudity(test_image)
                
                # Benchmark
                times = []
                for i in range(10):
                    start_time = time.time()
                    result = self.detector.detect_nudity(test_image)
                    end_time = time.time()
                    times.append(end_time - start_time)
                
                avg_time = sum(times) / len(times)
                min_time = min(times)
                max_time = max(times)
                fps = 1.0 / avg_time if avg_time > 0 else 0
                
                print(f"✓ {description} ({width}x{height}):")
                print(f"  Average: {avg_time*1000:.1f}ms ({fps:.1f} FPS)")
                print(f"  Range: {min_time*1000:.1f}ms - {max_time*1000:.1f}ms")
                
                # Performance should be reasonable for real-time use
                self.assertLess(avg_time, 1.0, f"Detection should complete within 1 second for {description}")
    
    def test_memory_usage_during_detection(self):
        """Test memory usage during detection"""
        if not self.model_available:
            self.skipTest("Model not available for memory testing")
        
        try:
            import psutil
            process = psutil.Process()
            
            # Get initial memory
            initial_memory = process.memory_info().rss / 1024 / 1024  # MB
            
            # Run multiple detections
            test_image = np.random.randint(0, 255, (720, 1280, 3), dtype=np.uint8)
            
            for i in range(20):
                result = self.detector.detect_nudity(test_image)
            
            # Get final memory
            final_memory = process.memory_info().rss / 1024 / 1024  # MB
            memory_increase = final_memory - initial_memory
            
            print(f"✓ Memory usage during detection:")
            print(f"  Initial: {initial_memory:.1f} MB")
            print(f"  Final: {final_memory:.1f} MB")
            print(f"  Increase: {memory_increase:.1f} MB")
            
            # Memory increase should be reasonable
            self.assertLess(memory_increase, 200, "Memory increase should be reasonable")
            
        except ImportError:
            self.skipTest("psutil not available for memory testing")

def run_detection_accuracy_tests():
    """Run all detection accuracy tests"""
    print("=" * 60)
    print("DETECTION ACCURACY TESTS")
    print("=" * 60)
    
    # Create test suite
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Add test classes
    suite.addTests(loader.loadTestsFromTestCase(TestDetectionAccuracy))
    suite.addTests(loader.loadTestsFromTestCase(TestDetectionPerformance))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    print("\n" + "=" * 60)
    if result.wasSuccessful():
        print("✓ ALL DETECTION ACCURACY TESTS PASSED!")
    else:
        print("✗ Some detection accuracy tests failed")
        for failure in result.failures:
            print(f"FAILURE: {failure[0]}")
        for error in result.errors:
            print(f"ERROR: {error[0]}")
    
    print("=" * 60)
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_detection_accuracy_tests()
    sys.exit(0 if success else 1)
