"""
System Tray Integration for Censor App
"""

import logging
from PyQt6.QtWidgets import QSystemTrayIcon, QMenu, QApplication
from PyQt6.QtCore import QObject, pyqtSignal
from PyQt6.QtGui import QIcon, QPixmap, QPainter, QColor
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from src.app_controller import AppController

class SystemTray(QObject):
    """System tray icon and menu for the application"""
    
    # Signals
    toggle_requested = pyqtSignal()
    settings_requested = pyqtSignal()
    quit_requested = pyqtSignal()
    
    def __init__(self, controller: 'AppController'):
        super().__init__()
        self.controller = controller
        self.logger = logging.getLogger(__name__)
        
        # Create system tray icon
        self.tray_icon = QSystemTrayIcon()
        self.tray_icon.setIcon(self._create_icon())
        self.tray_icon.setToolTip("Censor App - Inactive")
        
        # Create context menu
        self._create_menu()
        
        # Connect signals
        self.tray_icon.activated.connect(self._on_tray_activated)
        
        self.logger.info("System tray initialized")
    
    def _create_icon(self, active: bool = False) -> QIcon:
        """Create the system tray icon"""
        # Create a simple colored circle icon
        pixmap = QPixmap(16, 16)
        pixmap.fill(QColor(0, 0, 0, 0))  # Transparent background
        
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # Choose color based on state
        color = QColor(255, 0, 0) if active else QColor(128, 128, 128)
        painter.setBrush(color)
        painter.setPen(QColor(0, 0, 0))
        
        # Draw circle
        painter.drawEllipse(2, 2, 12, 12)
        painter.end()
        
        return QIcon(pixmap)
    
    def _create_menu(self):
        """Create the context menu for the system tray"""
        self.menu = QMenu()
        
        # Toggle action
        self.toggle_action = self.menu.addAction("Start Censoring")
        self.toggle_action.triggered.connect(self.toggle_requested.emit)
        
        self.menu.addSeparator()
        
        # Body part toggles
        self.breasts_action = self.menu.addAction("Toggle Breasts")
        self.breasts_action.triggered.connect(lambda: self._toggle_body_part('breasts'))
        
        self.genitals_action = self.menu.addAction("Toggle Genitals")
        self.genitals_action.triggered.connect(lambda: self._toggle_body_part('genitals'))
        
        self.buttocks_action = self.menu.addAction("Toggle Buttocks")
        self.buttocks_action.triggered.connect(lambda: self._toggle_body_part('buttocks'))
        
        self.menu.addSeparator()
        
        # Settings action
        self.settings_action = self.menu.addAction("Settings...")
        self.settings_action.triggered.connect(self.settings_requested.emit)
        
        self.menu.addSeparator()
        
        # Quit action
        self.quit_action = self.menu.addAction("Quit")
        self.quit_action.triggered.connect(self.quit_requested.emit)
        
        self.tray_icon.setContextMenu(self.menu)
    
    def _toggle_body_part(self, body_part: str):
        """Toggle censoring for a specific body part"""
        from src.config.settings import BodyPart
        
        try:
            body_part_enum = BodyPart(body_part)
            enabled = self.controller.settings.toggle_body_part(body_part_enum)
            self.logger.info(f"Toggled {body_part} censoring: {enabled}")
            
            # Update menu text
            action_map = {
                'breasts': self.breasts_action,
                'genitals': self.genitals_action,
                'buttocks': self.buttocks_action
            }
            
            if body_part in action_map:
                status = "✓" if enabled else "✗"
                action_map[body_part].setText(f"{status} {body_part.title()}")
            
        except Exception as e:
            self.logger.error(f"Error toggling {body_part}: {e}")
    
    def _on_tray_activated(self, reason):
        """Handle tray icon activation"""
        if reason == QSystemTrayIcon.ActivationReason.DoubleClick:
            self.settings_requested.emit()
        elif reason == QSystemTrayIcon.ActivationReason.MiddleClick:
            self.toggle_requested.emit()
    
    def show(self):
        """Show the system tray icon"""
        if not QSystemTrayIcon.isSystemTrayAvailable():
            self.logger.error("System tray is not available")
            return False
        
        self.tray_icon.show()
        self._update_menu_states()
        return True
    
    def hide(self):
        """Hide the system tray icon"""
        self.tray_icon.hide()
    
    def update_status(self, status: str):
        """Update the tray icon status"""
        self.tray_icon.setToolTip(f"Censor App - {status}")
        
        # Update icon based on status
        active = "active" in status.lower()
        self.tray_icon.setIcon(self._create_icon(active))
        
        # Update toggle action text
        if active:
            self.toggle_action.setText("Stop Censoring")
        else:
            self.toggle_action.setText("Start Censoring")
    
    def _update_menu_states(self):
        """Update menu item states based on current settings"""
        from src.config.settings import BodyPart
        
        # Update body part action texts
        for body_part in BodyPart:
            setting = self.controller.settings.get_body_part_setting(body_part)
            status = "✓" if setting.enabled else "✗"
            
            if body_part == BodyPart.BREASTS:
                self.breasts_action.setText(f"{status} Breasts")
            elif body_part == BodyPart.GENITALS:
                self.genitals_action.setText(f"{status} Genitals")
            elif body_part == BodyPart.BUTTOCKS:
                self.buttocks_action.setText(f"{status} Buttocks")
    
    def show_message(self, title: str, message: str, icon=QSystemTrayIcon.MessageIcon.Information):
        """Show a system tray notification"""
        if self.tray_icon.isVisible():
            self.tray_icon.showMessage(title, message, icon, 3000)  # 3 second timeout
