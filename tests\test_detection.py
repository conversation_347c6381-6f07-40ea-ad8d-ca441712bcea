"""
Tests for nudity detection functionality
"""

import unittest
import sys
import os
import numpy as np

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

try:
    from detection.nudity_detector import NudityDetector, Detection, DetectionResult
    from config.settings import BodyPart
    DETECTION_AVAILABLE = True
except ImportError as e:
    DETECTION_AVAILABLE = False
    print(f"Detection modules not available: {e}")

class TestNudityDetector(unittest.TestCase):
    """Test nudity detection functionality"""
    
    def setUp(self):
        """Set up test fixtures"""
        if not DETECTION_AVAILABLE:
            self.skipTest("Detection modules not available")
        
        self.detector = NudityDetector()
        # Create a test image
        self.test_frame = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
    
    def test_detector_initialization(self):
        """Test detector initialization"""
        self.assertIsNotNone(self.detector)
        self.assertFalse(self.detector.model_loaded)
        self.assertEqual(self.detector.confidence_threshold, 0.7)
        self.assertEqual(self.detector.input_size, (320, 320))
    
    def test_confidence_threshold_setting(self):
        """Test setting confidence threshold"""
        # Test valid threshold
        self.detector.set_confidence_threshold(0.5)
        self.assertEqual(self.detector.confidence_threshold, 0.5)
        
        self.detector.set_confidence_threshold(0.9)
        self.assertEqual(self.detector.confidence_threshold, 0.9)
        
        # Test invalid thresholds (should not change)
        original_threshold = self.detector.confidence_threshold
        self.detector.set_confidence_threshold(-0.1)
        self.assertEqual(self.detector.confidence_threshold, original_threshold)
        
        self.detector.set_confidence_threshold(1.5)
        self.assertEqual(self.detector.confidence_threshold, original_threshold)
    
    def test_input_size_setting(self):
        """Test setting input size"""
        # Test valid size
        self.detector.set_input_size(416, 416)
        self.assertEqual(self.detector.input_size, (416, 416))
        
        # Test invalid sizes (should not change)
        original_size = self.detector.input_size
        self.detector.set_input_size(0, 416)
        self.assertEqual(self.detector.input_size, original_size)
        
        self.detector.set_input_size(416, -1)
        self.assertEqual(self.detector.input_size, original_size)
    
    def test_frame_preprocessing(self):
        """Test frame preprocessing"""
        # Test with different frame sizes
        small_frame = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
        large_frame = np.random.randint(0, 255, (1080, 1920, 3), dtype=np.uint8)
        
        processed_small = self.detector.preprocess_frame(small_frame)
        processed_large = self.detector.preprocess_frame(large_frame)
        
        self.assertIsInstance(processed_small, np.ndarray)
        self.assertIsInstance(processed_large, np.ndarray)
        
        # Check that large frames are resized
        max_dim = max(processed_large.shape[:2])
        self.assertLessEqual(max_dim, max(self.detector.input_size))
    
    def test_class_mapping(self):
        """Test body part class mapping"""
        # Test known mappings
        self.assertEqual(
            NudityDetector.CLASS_MAPPING.get('FEMALE_BREAST_EXPOSED'),
            BodyPart.BREASTS
        )
        self.assertEqual(
            NudityDetector.CLASS_MAPPING.get('MALE_GENITALIA_EXPOSED'),
            BodyPart.GENITALS
        )
        self.assertEqual(
            NudityDetector.CLASS_MAPPING.get('BUTTOCKS_EXPOSED'),
            BodyPart.BUTTOCKS
        )
        
        # Test unknown class
        self.assertIsNone(
            NudityDetector.CLASS_MAPPING.get('UNKNOWN_CLASS')
        )
    
    def test_detection_result_structure(self):
        """Test detection result structure without actual model"""
        # Test empty detection result
        result = DetectionResult(
            detections=[],
            processing_time=0.1,
            frame_shape=(480, 640),
            timestamp=1234567890.0
        )
        
        self.assertIsInstance(result.detections, list)
        self.assertEqual(len(result.detections), 0)
        self.assertEqual(result.processing_time, 0.1)
        self.assertEqual(result.frame_shape, (480, 640))
        self.assertEqual(result.timestamp, 1234567890.0)
    
    def test_detection_structure(self):
        """Test detection structure"""
        detection = Detection(
            body_part=BodyPart.BREASTS,
            confidence=0.85,
            bbox=(0.1, 0.2, 0.3, 0.4),
            raw_class="FEMALE_BREAST_EXPOSED"
        )
        
        self.assertEqual(detection.body_part, BodyPart.BREASTS)
        self.assertEqual(detection.confidence, 0.85)
        self.assertEqual(detection.bbox, (0.1, 0.2, 0.3, 0.4))
        self.assertEqual(detection.raw_class, "FEMALE_BREAST_EXPOSED")
    
    def test_filter_detections_by_body_parts(self):
        """Test filtering detections by body parts"""
        # Create test detections
        detections = [
            Detection(BodyPart.BREASTS, 0.9, (0.1, 0.1, 0.2, 0.2), "BREAST"),
            Detection(BodyPart.GENITALS, 0.8, (0.3, 0.3, 0.4, 0.4), "GENITALS"),
            Detection(BodyPart.BUTTOCKS, 0.7, (0.5, 0.5, 0.6, 0.6), "BUTTOCKS"),
        ]
        
        # Test filtering for breasts only
        filtered = self.detector.filter_detections_by_body_parts(
            detections, [BodyPart.BREASTS]
        )
        self.assertEqual(len(filtered), 1)
        self.assertEqual(filtered[0].body_part, BodyPart.BREASTS)
        
        # Test filtering for multiple parts
        filtered = self.detector.filter_detections_by_body_parts(
            detections, [BodyPart.BREASTS, BodyPart.GENITALS]
        )
        self.assertEqual(len(filtered), 2)
        
        # Test filtering for no parts
        filtered = self.detector.filter_detections_by_body_parts(detections, [])
        self.assertEqual(len(filtered), 0)
    
    def test_performance_stats(self):
        """Test performance statistics"""
        stats = self.detector.get_performance_stats()
        
        self.assertIsInstance(stats, dict)
        self.assertIn('model_loaded', stats)
        self.assertIn('total_frames', stats)
        self.assertIn('total_detections', stats)
        self.assertIn('avg_detection_time_ms', stats)
        self.assertIn('confidence_threshold', stats)
        self.assertIn('detection_available', stats)
        
        # Check initial values
        self.assertFalse(stats['model_loaded'])
        self.assertEqual(stats['total_frames'], 0)
        self.assertEqual(stats['total_detections'], 0)
        self.assertEqual(stats['confidence_threshold'], 0.7)

class TestDetectionIntegration(unittest.TestCase):
    """Test detection integration (requires actual model)"""
    
    def setUp(self):
        """Set up test fixtures"""
        if not DETECTION_AVAILABLE:
            self.skipTest("Detection modules not available")
        
        # Check if we can actually load the model
        try:
            import nudenet
            self.detector = NudityDetector()
            self.model_available = True
        except ImportError:
            self.model_available = False
            self.skipTest("NudeNet not available")
    
    def test_model_loading(self):
        """Test model loading (if available)"""
        if not self.model_available:
            self.skipTest("Model not available")
        
        # This test will download the model on first run
        # It may take a while and require internet connection
        result = self.detector.load_model()
        
        if result:
            self.assertTrue(self.detector.model_loaded)
            self.assertIsNotNone(self.detector.model)
        else:
            self.skipTest("Model loading failed (network/dependency issue)")
    
    def test_detection_with_model(self):
        """Test actual detection with model (if available)"""
        if not self.model_available:
            self.skipTest("Model not available")
        
        # Load model first
        if not self.detector.load_model():
            self.skipTest("Model loading failed")
        
        # Create a test frame
        test_frame = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
        
        # Run detection
        result = self.detector.detect_nudity(test_frame)
        
        # Check result structure
        self.assertIsInstance(result, DetectionResult)
        self.assertIsInstance(result.detections, list)
        self.assertGreater(result.processing_time, 0)
        self.assertEqual(result.frame_shape, (480, 640))
        self.assertGreater(result.timestamp, 0)

if __name__ == '__main__':
    unittest.main()
