"""
Main Settings Window for Censor App
"""

import logging
from PyQt6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                            QLabel, QCheckBox, QComboBox, QSlider, QPushButton,
                            QGroupBox, QGridLayout, QSpinBox, QLineEdit,
                            QColorDialog, QFileDialog, QTabWidget, QTextEdit)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont, QColor
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from src.app_controller import AppController

class MainWindow(QMainWindow):
    """Main settings window for the application"""
    
    # Signals
    settings_changed = pyqtSignal()
    
    def __init__(self, controller: 'AppController'):
        super().__init__()
        self.controller = controller
        self.logger = logging.getLogger(__name__)

        self.setWindowTitle("Censor App - Settings")
        self.setFixedSize(700, 600)  # Increased size for better layout

        # Connect to controller signals for real-time updates
        if hasattr(controller, 'state_changed'):
            controller.state_changed.connect(self._on_state_changed)
        if hasattr(controller, 'status_changed'):
            controller.status_changed.connect(self.update_status)
        if hasattr(controller, 'error_occurred'):
            controller.error_occurred.connect(self._on_error_occurred)
        
        # Create central widget and layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Create tab widget
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # Create tabs
        self._create_status_tab()      # New status tab first
        self._create_general_tab()
        self._create_detection_tab()
        self._create_censoring_tab()
        self._create_profiles_tab()    # New profiles tab
        self._create_performance_tab()
        self._create_hotkeys_tab()
        
        # Status bar
        self.status_label = QLabel("Ready")
        layout.addWidget(self.status_label)
        
        # Buttons
        button_layout = QHBoxLayout()
        
        self.apply_button = QPushButton("Apply")
        self.apply_button.clicked.connect(self._apply_settings)
        
        self.ok_button = QPushButton("OK")
        self.ok_button.clicked.connect(self._ok_clicked)
        
        self.cancel_button = QPushButton("Cancel")
        self.cancel_button.clicked.connect(self.close)
        
        button_layout.addStretch()
        button_layout.addWidget(self.apply_button)
        button_layout.addWidget(self.ok_button)
        button_layout.addWidget(self.cancel_button)
        
        layout.addLayout(button_layout)
        
        # Load current settings
        self._load_settings()
        
        self.logger.info("Main window initialized")

    def _create_status_tab(self):
        """Create the status monitoring tab"""
        from PyQt6.QtWidgets import QTextEdit, QProgressBar
        from PyQt6.QtCore import QTimer

        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Application status group
        status_group = QGroupBox("Application Status")
        status_layout = QGridLayout(status_group)

        # Current state
        status_layout.addWidget(QLabel("Current State:"), 0, 0)
        self.state_label = QLabel("Stopped")
        self.state_label.setStyleSheet("font-weight: bold; color: red;")
        status_layout.addWidget(self.state_label, 0, 1)

        # Control buttons
        self.start_stop_button = QPushButton("Start Censoring")
        self.start_stop_button.clicked.connect(self._toggle_censoring)
        status_layout.addWidget(self.start_stop_button, 0, 2)

        layout.addWidget(status_group)

        # Performance monitoring group
        perf_group = QGroupBox("Performance Monitoring")
        perf_layout = QGridLayout(perf_group)

        # FPS display
        perf_layout.addWidget(QLabel("Capture FPS:"), 0, 0)
        self.capture_fps_label = QLabel("0")
        perf_layout.addWidget(self.capture_fps_label, 0, 1)

        perf_layout.addWidget(QLabel("Processing FPS:"), 1, 0)
        self.processing_fps_label = QLabel("0")
        perf_layout.addWidget(self.processing_fps_label, 1, 1)

        # Queue status
        perf_layout.addWidget(QLabel("Queue Size:"), 2, 0)
        self.queue_size_label = QLabel("0")
        perf_layout.addWidget(self.queue_size_label, 2, 1)

        # Dropped frames
        perf_layout.addWidget(QLabel("Dropped Frames:"), 3, 0)
        self.dropped_frames_label = QLabel("0")
        perf_layout.addWidget(self.dropped_frames_label, 3, 1)

        # Advanced performance metrics
        perf_layout.addWidget(QLabel("CPU Usage:"), 4, 0)
        self.cpu_usage_label = QLabel("0%")
        perf_layout.addWidget(self.cpu_usage_label, 4, 1)

        perf_layout.addWidget(QLabel("Memory Usage:"), 5, 0)
        self.memory_usage_label = QLabel("0 MB")
        perf_layout.addWidget(self.memory_usage_label, 5, 1)

        perf_layout.addWidget(QLabel("Cache Hit Rate:"), 6, 0)
        self.cache_hits_label = QLabel("0%")
        perf_layout.addWidget(self.cache_hits_label, 6, 1)

        perf_layout.addWidget(QLabel("Quality Level:"), 7, 0)
        self.quality_level_label = QLabel("Level 3")
        perf_layout.addWidget(self.quality_level_label, 7, 1)

        perf_layout.addWidget(QLabel("ROI Regions:"), 8, 0)
        self.roi_regions_label = QLabel("0")
        perf_layout.addWidget(self.roi_regions_label, 8, 1)

        layout.addWidget(perf_group)

        # Component status group
        comp_group = QGroupBox("Component Status")
        comp_layout = QGridLayout(comp_group)

        self.component_labels = {}
        components = ['Screen Capture', 'Detection Engine', 'Overlay Renderer', 'Processing Thread']
        for i, comp in enumerate(components):
            comp_layout.addWidget(QLabel(f"{comp}:"), i, 0)
            label = QLabel("Unknown")
            self.component_labels[comp.lower().replace(' ', '_')] = label
            comp_layout.addWidget(label, i, 1)

        layout.addWidget(comp_group)

        # Log display
        log_group = QGroupBox("Recent Activity")
        log_layout = QVBoxLayout(log_group)

        self.log_display = QTextEdit()
        self.log_display.setMaximumHeight(150)
        self.log_display.setReadOnly(True)
        log_layout.addWidget(self.log_display)

        layout.addWidget(log_group)

        # Auto-refresh timer
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self._refresh_status)
        self.refresh_timer.start(1000)  # Update every second

        self.tab_widget.addTab(tab, "Status")

    def _create_general_tab(self):
        """Create the general settings tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Main enable/disable
        self.enabled_checkbox = QCheckBox("Enable Censoring")
        layout.addWidget(self.enabled_checkbox)
        
        # Body parts group
        body_parts_group = QGroupBox("Body Parts")
        body_parts_layout = QVBoxLayout(body_parts_group)
        
        self.breasts_checkbox = QCheckBox("Censor Breasts")
        self.genitals_checkbox = QCheckBox("Censor Genitals")
        self.buttocks_checkbox = QCheckBox("Censor Buttocks")
        
        body_parts_layout.addWidget(self.breasts_checkbox)
        body_parts_layout.addWidget(self.genitals_checkbox)
        body_parts_layout.addWidget(self.buttocks_checkbox)
        
        layout.addWidget(body_parts_group)
        layout.addStretch()
        
        self.tab_widget.addTab(tab, "General")
    
    def _create_detection_tab(self):
        """Create the detection settings tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Detection settings group
        detection_group = QGroupBox("Detection Settings")
        detection_layout = QGridLayout(detection_group)
        
        # Confidence threshold
        detection_layout.addWidget(QLabel("Confidence Threshold:"), 0, 0)
        self.confidence_slider = QSlider(Qt.Orientation.Horizontal)
        self.confidence_slider.setRange(10, 100)
        self.confidence_slider.setValue(70)
        self.confidence_label = QLabel("0.70")
        self.confidence_slider.valueChanged.connect(
            lambda v: self.confidence_label.setText(f"{v/100:.2f}")
        )
        detection_layout.addWidget(self.confidence_slider, 0, 1)
        detection_layout.addWidget(self.confidence_label, 0, 2)
        
        # Frame skip
        detection_layout.addWidget(QLabel("Frame Skip:"), 1, 0)
        self.frame_skip_spinbox = QSpinBox()
        self.frame_skip_spinbox.setRange(1, 10)
        self.frame_skip_spinbox.setValue(2)
        detection_layout.addWidget(self.frame_skip_spinbox, 1, 1)
        
        # GPU acceleration
        self.gpu_checkbox = QCheckBox("Enable GPU Acceleration")
        detection_layout.addWidget(self.gpu_checkbox, 2, 0, 1, 2)
        
        # ROI optimization
        self.roi_checkbox = QCheckBox("Enable Region of Interest Optimization")
        detection_layout.addWidget(self.roi_checkbox, 3, 0, 1, 2)
        
        layout.addWidget(detection_group)
        layout.addStretch()
        
        self.tab_widget.addTab(tab, "Detection")
    
    def _create_censoring_tab(self):
        """Create the advanced censoring settings tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Create scroll area for all the settings
        from PyQt6.QtWidgets import QScrollArea
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)

        from src.config.settings import BodyPart, CensorMethod

        # Store references to controls for each body part
        self.censoring_controls = {}

        for body_part in BodyPart:
            # Create controls dictionary for this body part
            controls = {}

            group = QGroupBox(f"{body_part.value.title()} Advanced Censoring Settings")
            group_layout = QGridLayout(group)

            # Method selection with all advanced options
            group_layout.addWidget(QLabel("Method:"), 0, 0)
            method_combo = QComboBox()
            method_items = [
                ("Blur", CensorMethod.BLUR),
                ("Light Blur", CensorMethod.BLUR_LIGHT),
                ("Heavy Blur", CensorMethod.BLUR_HEAVY),
                ("Pixelate", CensorMethod.PIXELATE),
                ("Light Pixelate", CensorMethod.PIXELATE_LIGHT),
                ("Heavy Pixelate", CensorMethod.PIXELATE_HEAVY),
                ("Black Bar", CensorMethod.BLACK_BAR),
                ("White Bar", CensorMethod.WHITE_BAR),
                ("Solid Color", CensorMethod.SOLID_COLOR),
                ("Mosaic", CensorMethod.MOSAIC),
                ("Noise", CensorMethod.NOISE),
                ("Swirl", CensorMethod.SWIRL),
                ("Custom Image", CensorMethod.CUSTOM_IMAGE)
            ]

            for display_name, method in method_items:
                method_combo.addItem(display_name, method)

            method_combo.currentIndexChanged.connect(
                lambda idx, bp=body_part: self._on_method_changed(bp, idx)
            )
            controls['method_combo'] = method_combo
            group_layout.addWidget(method_combo, 0, 1, 1, 2)

            # Intensity
            group_layout.addWidget(QLabel("Intensity:"), 1, 0)
            intensity_slider = QSlider(Qt.Orientation.Horizontal)
            intensity_slider.setRange(10, 100)
            intensity_slider.setValue(80)
            intensity_label = QLabel("0.80")
            intensity_slider.valueChanged.connect(
                lambda v, label=intensity_label: label.setText(f"{v/100:.2f}")
            )
            controls['intensity_slider'] = intensity_slider
            controls['intensity_label'] = intensity_label
            group_layout.addWidget(intensity_slider, 1, 1)
            group_layout.addWidget(intensity_label, 1, 2)

            # Opacity
            group_layout.addWidget(QLabel("Opacity:"), 2, 0)
            opacity_slider = QSlider(Qt.Orientation.Horizontal)
            opacity_slider.setRange(10, 100)
            opacity_slider.setValue(80)
            opacity_label = QLabel("0.80")
            opacity_slider.valueChanged.connect(
                lambda v, label=opacity_label: label.setText(f"{v/100:.2f}")
            )
            controls['opacity_slider'] = opacity_slider
            controls['opacity_label'] = opacity_label
            group_layout.addWidget(opacity_slider, 2, 1)
            group_layout.addWidget(opacity_label, 2, 2)

            # Blur Radius (for blur methods)
            group_layout.addWidget(QLabel("Blur Radius:"), 3, 0)
            blur_radius_spinbox = QSpinBox()
            blur_radius_spinbox.setRange(1, 50)
            blur_radius_spinbox.setValue(15)
            controls['blur_radius_spinbox'] = blur_radius_spinbox
            group_layout.addWidget(blur_radius_spinbox, 3, 1)

            # Pixel Size (for pixelate methods)
            group_layout.addWidget(QLabel("Pixel Size:"), 4, 0)
            pixel_size_spinbox = QSpinBox()
            pixel_size_spinbox.setRange(2, 30)
            pixel_size_spinbox.setValue(10)
            controls['pixel_size_spinbox'] = pixel_size_spinbox
            group_layout.addWidget(pixel_size_spinbox, 4, 1)

            # Shape selection
            group_layout.addWidget(QLabel("Shape:"), 5, 0)
            shape_combo = QComboBox()
            shape_combo.addItems(["Rectangle", "Ellipse", "Rounded Rectangle"])
            controls['shape_combo'] = shape_combo
            group_layout.addWidget(shape_combo, 5, 1)

            # Color selection
            color_button = QPushButton("Choose Color")
            color_button.clicked.connect(lambda checked, bp=body_part: self._choose_color(bp))
            controls['color_button'] = color_button
            group_layout.addWidget(color_button, 6, 0)

            # Custom image
            image_button = QPushButton("Choose Image")
            image_button.clicked.connect(lambda checked, bp=body_part: self._choose_image(bp))
            controls['image_button'] = image_button
            group_layout.addWidget(image_button, 6, 1)

            # Padding
            group_layout.addWidget(QLabel("Padding:"), 7, 0)
            padding_spinbox = QSpinBox()
            padding_spinbox.setRange(0, 20)
            padding_spinbox.setValue(5)
            controls['padding_spinbox'] = padding_spinbox
            group_layout.addWidget(padding_spinbox, 7, 1)

            # Preview button
            preview_button = QPushButton("Preview Effect")
            preview_button.clicked.connect(lambda checked, bp=body_part: self._preview_effect(bp))
            controls['preview_button'] = preview_button
            group_layout.addWidget(preview_button, 8, 0, 1, 2)

            scroll_layout.addWidget(group)
            self.censoring_controls[body_part] = controls

        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        layout.addWidget(scroll_area)

        self.tab_widget.addTab(tab, "Censoring")

    def _create_profiles_tab(self):
        """Create the user preference profiles tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Profile management group
        profile_group = QGroupBox("Profile Management")
        profile_layout = QGridLayout(profile_group)

        # Current profile selection
        profile_layout.addWidget(QLabel("Current Profile:"), 0, 0)
        self.profile_combo = QComboBox()
        self.profile_combo.addItems(["Default", "Work Safe", "Maximum Privacy", "Custom"])
        self.profile_combo.currentTextChanged.connect(self._on_profile_changed)
        profile_layout.addWidget(self.profile_combo, 0, 1)

        # Profile actions
        self.save_profile_button = QPushButton("Save Current as Profile")
        self.save_profile_button.clicked.connect(self._save_current_profile)
        profile_layout.addWidget(self.save_profile_button, 1, 0)

        self.delete_profile_button = QPushButton("Delete Profile")
        self.delete_profile_button.clicked.connect(self._delete_profile)
        profile_layout.addWidget(self.delete_profile_button, 1, 1)

        self.export_profile_button = QPushButton("Export Profile")
        self.export_profile_button.clicked.connect(self._export_profile)
        profile_layout.addWidget(self.export_profile_button, 2, 0)

        self.import_profile_button = QPushButton("Import Profile")
        self.import_profile_button.clicked.connect(self._import_profile)
        profile_layout.addWidget(self.import_profile_button, 2, 1)

        layout.addWidget(profile_group)

        # Quick presets group
        presets_group = QGroupBox("Quick Presets")
        presets_layout = QGridLayout(presets_group)

        # Preset buttons
        self.minimal_preset_button = QPushButton("Minimal Censoring")
        self.minimal_preset_button.clicked.connect(lambda: self._apply_preset("minimal"))
        presets_layout.addWidget(self.minimal_preset_button, 0, 0)

        self.moderate_preset_button = QPushButton("Moderate Censoring")
        self.moderate_preset_button.clicked.connect(lambda: self._apply_preset("moderate"))
        presets_layout.addWidget(self.moderate_preset_button, 0, 1)

        self.maximum_preset_button = QPushButton("Maximum Censoring")
        self.maximum_preset_button.clicked.connect(lambda: self._apply_preset("maximum"))
        presets_layout.addWidget(self.maximum_preset_button, 1, 0)

        self.artistic_preset_button = QPushButton("Artistic Effects")
        self.artistic_preset_button.clicked.connect(lambda: self._apply_preset("artistic"))
        presets_layout.addWidget(self.artistic_preset_button, 1, 1)

        layout.addWidget(presets_group)

        # Profile description
        description_group = QGroupBox("Profile Description")
        description_layout = QVBoxLayout(description_group)

        self.profile_description = QTextEdit()
        self.profile_description.setMaximumHeight(100)
        self.profile_description.setPlaceholderText("Enter a description for this profile...")
        description_layout.addWidget(self.profile_description)

        layout.addWidget(description_group)
        layout.addStretch()

        self.tab_widget.addTab(tab, "Profiles")

    def _create_performance_tab(self):
        """Create the performance settings tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Performance settings group
        perf_group = QGroupBox("Performance Settings")
        perf_layout = QGridLayout(perf_group)
        
        # Max FPS
        perf_layout.addWidget(QLabel("Max FPS:"), 0, 0)
        self.fps_spinbox = QSpinBox()
        self.fps_spinbox.setRange(5, 60)
        self.fps_spinbox.setValue(30)
        perf_layout.addWidget(self.fps_spinbox, 0, 1)
        
        # Thread count
        perf_layout.addWidget(QLabel("Thread Count:"), 1, 0)
        self.thread_spinbox = QSpinBox()
        self.thread_spinbox.setRange(1, 16)
        self.thread_spinbox.setValue(4)
        perf_layout.addWidget(self.thread_spinbox, 1, 1)
        
        # Memory limit
        perf_layout.addWidget(QLabel("Memory Limit (MB):"), 2, 0)
        self.memory_spinbox = QSpinBox()
        self.memory_spinbox.setRange(256, 4096)
        self.memory_spinbox.setValue(1024)
        perf_layout.addWidget(self.memory_spinbox, 2, 1)
        
        layout.addWidget(perf_group)
        layout.addStretch()
        
        self.tab_widget.addTab(tab, "Performance")
    
    def _create_hotkeys_tab(self):
        """Create the enhanced hotkeys settings tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Create scroll area for all hotkey settings
        from PyQt6.QtWidgets import QScrollArea
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)

        # Basic hotkeys group
        basic_group = QGroupBox("Basic Controls")
        basic_layout = QGridLayout(basic_group)

        # Advanced hotkeys group
        advanced_group = QGroupBox("Advanced Controls")
        advanced_layout = QGridLayout(advanced_group)

        # Hotkey settings group
        settings_group = QGroupBox("Hotkey Settings")
        settings_layout = QGridLayout(settings_group)

        # Create hotkey input fields
        self.hotkey_inputs = {}

        # Basic hotkey labels
        basic_hotkeys = {
            'toggle_censoring': 'Toggle Censoring:',
            'toggle_breasts': 'Toggle Breasts:',
            'toggle_genitals': 'Toggle Genitals:',
            'toggle_buttocks': 'Toggle Buttocks:',
            'emergency_disable': 'Emergency Disable:'
        }

        # Advanced hotkey labels
        advanced_hotkeys = {
            'quick_preset_minimal': 'Quick Preset - Minimal:',
            'quick_preset_moderate': 'Quick Preset - Moderate:',
            'quick_preset_maximum': 'Quick Preset - Maximum:',
            'temporary_disable': 'Temporary Disable:',
            'stealth_mode': 'Stealth Mode:',
            'increase_intensity': 'Increase Intensity:',
            'decrease_intensity': 'Decrease Intensity:'
        }

        # Create basic hotkey inputs
        for i, (key, label) in enumerate(basic_hotkeys.items()):
            basic_layout.addWidget(QLabel(label), i, 0)
            line_edit = self._create_hotkey_input()
            self.hotkey_inputs[key] = line_edit
            basic_layout.addWidget(line_edit, i, 1)

            # Add clear button
            clear_btn = QPushButton("Clear")
            clear_btn.clicked.connect(lambda checked, k=key: self.hotkey_inputs[k].clear())
            basic_layout.addWidget(clear_btn, i, 2)

        # Create advanced hotkey inputs
        for i, (key, label) in enumerate(advanced_hotkeys.items()):
            advanced_layout.addWidget(QLabel(label), i, 0)
            line_edit = self._create_hotkey_input()
            self.hotkey_inputs[key] = line_edit
            advanced_layout.addWidget(line_edit, i, 1)

            # Add clear button
            clear_btn = QPushButton("Clear")
            clear_btn.clicked.connect(lambda checked, k=key: self.hotkey_inputs[k].clear())
            advanced_layout.addWidget(clear_btn, i, 2)

        # Hotkey settings
        settings_layout.addWidget(QLabel("Enable Hotkeys:"), 0, 0)
        self.hotkeys_enabled_checkbox = QCheckBox()
        settings_layout.addWidget(self.hotkeys_enabled_checkbox, 0, 1)

        settings_layout.addWidget(QLabel("Cooldown (ms):"), 1, 0)
        self.hotkey_cooldown_spinbox = QSpinBox()
        self.hotkey_cooldown_spinbox.setRange(100, 5000)
        self.hotkey_cooldown_spinbox.setValue(500)
        settings_layout.addWidget(self.hotkey_cooldown_spinbox, 1, 1)

        settings_layout.addWidget(QLabel("Temp Disable Duration (s):"), 2, 0)
        self.temp_disable_duration_spinbox = QSpinBox()
        self.temp_disable_duration_spinbox.setRange(5, 300)
        self.temp_disable_duration_spinbox.setValue(30)
        settings_layout.addWidget(self.temp_disable_duration_spinbox, 2, 1)

        # Add groups to scroll layout
        scroll_layout.addWidget(basic_group)
        scroll_layout.addWidget(advanced_group)
        scroll_layout.addWidget(settings_group)
        scroll_layout.addStretch()

        # Setup scroll area
        scroll_widget.setLayout(scroll_layout)
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)

        layout.addWidget(scroll_area)

        # Add hotkey status and test section
        status_group = QGroupBox("Hotkey Status")
        status_layout = QVBoxLayout(status_group)

        self.hotkey_status_label = QLabel("Hotkeys: Not initialized")
        status_layout.addWidget(self.hotkey_status_label)

        test_layout = QHBoxLayout()
        test_btn = QPushButton("Test Hotkeys")
        test_btn.clicked.connect(self._test_hotkeys)
        test_layout.addWidget(test_btn)

        reset_btn = QPushButton("Reset to Defaults")
        reset_btn.clicked.connect(self._reset_hotkeys_to_defaults)
        test_layout.addWidget(reset_btn)

        status_layout.addLayout(test_layout)
        layout.addWidget(status_group)

        self.tab_widget.addTab(tab, "Hotkeys")

    def _create_hotkey_input(self):
        """Create a specialized hotkey input field"""
        line_edit = QLineEdit()
        line_edit.setPlaceholderText("Click and press key combination")
        line_edit.setReadOnly(True)  # Prevent typing, only capture key events

        # Install event filter for key capture
        line_edit.installEventFilter(self)
        line_edit.mousePressEvent = lambda event: self._start_hotkey_capture(line_edit)

        return line_edit

    def _start_hotkey_capture(self, line_edit):
        """Start capturing hotkey for the given input field"""
        line_edit.setText("Press key combination...")
        line_edit.setStyleSheet("background-color: #ffffcc;")  # Highlight during capture
        self._capturing_hotkey = line_edit

    def eventFilter(self, obj, event):
        """Event filter to capture hotkey combinations"""
        if hasattr(self, '_capturing_hotkey') and obj == self._capturing_hotkey:
            from PyQt6.QtCore import QEvent
            from PyQt6.QtGui import QKeyEvent

            if event.type() == QEvent.Type.KeyPress:
                key_event = event
                modifiers = key_event.modifiers()
                key = key_event.key()

                # Build key combination string
                key_combo = []

                if modifiers & Qt.KeyboardModifier.ControlModifier:
                    key_combo.append("ctrl")
                if modifiers & Qt.KeyboardModifier.ShiftModifier:
                    key_combo.append("shift")
                if modifiers & Qt.KeyboardModifier.AltModifier:
                    key_combo.append("alt")
                if modifiers & Qt.KeyboardModifier.MetaModifier:
                    key_combo.append("meta")

                # Add the main key
                key_name = self._get_key_name(key)
                if key_name:
                    key_combo.append(key_name)

                if len(key_combo) > 1:  # Must have at least one modifier
                    hotkey_string = "+".join(key_combo)
                    obj.setText(hotkey_string)
                    obj.setStyleSheet("")  # Remove highlight
                    self._capturing_hotkey = None
                    return True

        return super().eventFilter(obj, event)

    def _get_key_name(self, key):
        """Convert Qt key code to string representation"""
        from PyQt6.QtCore import Qt

        key_map = {
            Qt.Key.Key_A: 'a', Qt.Key.Key_B: 'b', Qt.Key.Key_C: 'c', Qt.Key.Key_D: 'd',
            Qt.Key.Key_E: 'e', Qt.Key.Key_F: 'f', Qt.Key.Key_G: 'g', Qt.Key.Key_H: 'h',
            Qt.Key.Key_I: 'i', Qt.Key.Key_J: 'j', Qt.Key.Key_K: 'k', Qt.Key.Key_L: 'l',
            Qt.Key.Key_M: 'm', Qt.Key.Key_N: 'n', Qt.Key.Key_O: 'o', Qt.Key.Key_P: 'p',
            Qt.Key.Key_Q: 'q', Qt.Key.Key_R: 'r', Qt.Key.Key_S: 's', Qt.Key.Key_T: 't',
            Qt.Key.Key_U: 'u', Qt.Key.Key_V: 'v', Qt.Key.Key_W: 'w', Qt.Key.Key_X: 'x',
            Qt.Key.Key_Y: 'y', Qt.Key.Key_Z: 'z',
            Qt.Key.Key_0: '0', Qt.Key.Key_1: '1', Qt.Key.Key_2: '2', Qt.Key.Key_3: '3',
            Qt.Key.Key_4: '4', Qt.Key.Key_5: '5', Qt.Key.Key_6: '6', Qt.Key.Key_7: '7',
            Qt.Key.Key_8: '8', Qt.Key.Key_9: '9',
            Qt.Key.Key_F1: 'f1', Qt.Key.Key_F2: 'f2', Qt.Key.Key_F3: 'f3', Qt.Key.Key_F4: 'f4',
            Qt.Key.Key_F5: 'f5', Qt.Key.Key_F6: 'f6', Qt.Key.Key_F7: 'f7', Qt.Key.Key_F8: 'f8',
            Qt.Key.Key_F9: 'f9', Qt.Key.Key_F10: 'f10', Qt.Key.Key_F11: 'f11', Qt.Key.Key_F12: 'f12',
            Qt.Key.Key_Space: 'space', Qt.Key.Key_Return: 'enter', Qt.Key.Key_Enter: 'enter',
            Qt.Key.Key_Escape: 'esc', Qt.Key.Key_Tab: 'tab', Qt.Key.Key_Backspace: 'backspace',
            Qt.Key.Key_Delete: 'delete', Qt.Key.Key_Insert: 'insert', Qt.Key.Key_Home: 'home',
            Qt.Key.Key_End: 'end', Qt.Key.Key_PageUp: 'page up', Qt.Key.Key_PageDown: 'page down',
            Qt.Key.Key_Up: 'up', Qt.Key.Key_Down: 'down', Qt.Key.Key_Left: 'left', Qt.Key.Key_Right: 'right',
            Qt.Key.Key_Plus: 'plus', Qt.Key.Key_Minus: 'minus', Qt.Key.Key_Equal: 'equal'
        }

        return key_map.get(key, None)

    def _test_hotkeys(self):
        """Test hotkey functionality"""
        if self.controller and hasattr(self.controller, 'hotkey_manager'):
            status = self.controller.hotkey_manager.get_status()

            message = f"Hotkey Manager Status:\n"
            message += f"Available: {status['available']}\n"
            message += f"Enabled: {status['enabled']}\n"
            message += f"Stealth Mode: {status['stealth_mode']}\n"
            message += f"Registered Hotkeys: {status['registered_count']}\n\n"

            if status['active_bindings']:
                message += "Active Bindings:\n"
                for binding in status['active_bindings']:
                    enabled_str = "✓" if binding['enabled'] else "✗"
                    message += f"{enabled_str} {binding['key']} - {binding['description']}\n"

            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.information(self, "Hotkey Test", message)
        else:
            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.warning(self, "Hotkey Test", "Hotkey manager not available")

    def _reset_hotkeys_to_defaults(self):
        """Reset all hotkeys to default values"""
        defaults = {
            'toggle_censoring': 'ctrl+shift+c',
            'toggle_breasts': 'ctrl+shift+b',
            'toggle_genitals': 'ctrl+shift+g',
            'toggle_buttocks': 'ctrl+shift+u',
            'emergency_disable': 'ctrl+shift+x',
            'quick_preset_minimal': 'ctrl+shift+1',
            'quick_preset_moderate': 'ctrl+shift+2',
            'quick_preset_maximum': 'ctrl+shift+3',
            'temporary_disable': 'ctrl+shift+t',
            'stealth_mode': 'ctrl+shift+s',
            'increase_intensity': 'ctrl+shift+plus',
            'decrease_intensity': 'ctrl+shift+minus'
        }

        for key, default_value in defaults.items():
            if key in self.hotkey_inputs:
                self.hotkey_inputs[key].setText(default_value)

        # Reset settings
        self.hotkeys_enabled_checkbox.setChecked(True)
        self.hotkey_cooldown_spinbox.setValue(500)
        self.temp_disable_duration_spinbox.setValue(30)
    
    def _choose_color(self, body_part):
        """Open color chooser dialog"""
        color = QColorDialog.getColor()
        if color.isValid():
            # Update new controls structure
            if hasattr(self, 'censoring_controls') and body_part in self.censoring_controls:
                controls = self.censoring_controls[body_part]
                if 'color_button' in controls:
                    button = controls['color_button']
                    button.setStyleSheet(f"background-color: {color.name()}")
                    button.setText(color.name())
            # Fallback to old structure
            elif hasattr(self, 'body_part_widgets') and body_part in self.body_part_widgets:
                button = self.body_part_widgets[body_part]['color_button']
                button.setStyleSheet(f"background-color: {color.name()}")
                button.setText(color.name())

    def _choose_image(self, body_part):
        """Open file chooser dialog for custom image"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Choose Custom Image", "",
            "Image Files (*.png *.jpg *.jpeg *.bmp *.gif)"
        )
        if file_path:
            # Update new controls structure
            if hasattr(self, 'censoring_controls') and body_part in self.censoring_controls:
                controls = self.censoring_controls[body_part]
                if 'image_button' in controls:
                    button = controls['image_button']
                    import os
                    filename = os.path.basename(file_path)
                    button.setText(f"Image: {filename[:15]}...")
                    button.setToolTip(file_path)
            # Fallback to old structure
            elif hasattr(self, 'body_part_widgets') and body_part in self.body_part_widgets:
                button = self.body_part_widgets[body_part]['image_button']
                button.setText(f"Image: {file_path.split('/')[-1]}")
                button.setToolTip(file_path)

    def _on_method_changed(self, body_part, index):
        """Handle method change for a body part"""
        if hasattr(self, 'censoring_controls'):
            controls = self.censoring_controls.get(body_part)
            if controls:
                method_combo = controls['method_combo']
                selected_method = method_combo.itemData(index)

                from src.config.settings import CensorMethod

                # Enable/disable controls based on method
                # Enable blur controls for blur methods
                blur_enabled = selected_method in [
                    CensorMethod.BLUR, CensorMethod.BLUR_LIGHT, CensorMethod.BLUR_HEAVY
                ]
                if 'blur_radius_spinbox' in controls:
                    controls['blur_radius_spinbox'].setEnabled(blur_enabled)

                # Enable pixel controls for pixelate methods
                pixel_enabled = selected_method in [
                    CensorMethod.PIXELATE, CensorMethod.PIXELATE_LIGHT, CensorMethod.PIXELATE_HEAVY
                ]
                if 'pixel_size_spinbox' in controls:
                    controls['pixel_size_spinbox'].setEnabled(pixel_enabled)

                # Enable color controls for solid color method
                color_enabled = selected_method == CensorMethod.SOLID_COLOR
                if 'color_button' in controls:
                    controls['color_button'].setEnabled(color_enabled)

                # Enable image controls for custom image method
                image_enabled = selected_method == CensorMethod.CUSTOM_IMAGE
                if 'image_button' in controls:
                    controls['image_button'].setEnabled(image_enabled)

    def _preview_effect(self, body_part):
        """Preview the censoring effect for a body part"""
        from PyQt6.QtWidgets import QDialog, QVBoxLayout, QLabel

        dialog = QDialog(self)
        dialog.setWindowTitle(f"Preview: {body_part.value.title()} Censoring")
        dialog.setFixedSize(400, 300)

        layout = QVBoxLayout(dialog)

        # Get current settings for this body part
        settings = self._get_current_body_part_settings(body_part)

        # Create preview label
        label = QLabel(f"Preview of {settings.method.value} effect")
        label.setStyleSheet("border: 1px solid gray; padding: 20px; background-color: #f0f0f0;")
        layout.addWidget(label)

        # Add settings info
        info_label = QLabel(
            f"Method: {settings.method.value}\n"
            f"Intensity: {settings.intensity:.2f}\n"
            f"Opacity: {settings.opacity:.2f}\n"
            f"Blur Radius: {settings.blur_radius}\n"
            f"Pixel Size: {settings.pixel_size}\n"
            f"Shape: {settings.shape}"
        )
        layout.addWidget(info_label)

        dialog.exec()

    def _get_current_body_part_settings(self, body_part):
        """Get current settings for a body part from UI controls"""
        from src.config.settings import CensorSettings, CensorMethod

        if hasattr(self, 'censoring_controls') and body_part in self.censoring_controls:
            controls = self.censoring_controls[body_part]

            # Get method
            method_combo = controls.get('method_combo')
            method = method_combo.currentData() if method_combo else CensorMethod.BLUR

            # Get other values
            intensity = controls.get('intensity_slider', type('obj', (), {'value': lambda: 80})).value() / 100.0
            opacity = controls.get('opacity_slider', type('obj', (), {'value': lambda: 80})).value() / 100.0
            blur_radius = controls.get('blur_radius_spinbox', type('obj', (), {'value': lambda: 15})).value()
            pixel_size = controls.get('pixel_size_spinbox', type('obj', (), {'value': lambda: 10})).value()
            shape_combo = controls.get('shape_combo')
            shape = shape_combo.currentText().lower().replace(' ', '_') if shape_combo else 'rectangle'

            return CensorSettings(
                method=method,
                intensity=intensity,
                opacity=opacity,
                blur_radius=blur_radius,
                pixel_size=pixel_size,
                shape=shape
            )

        # Fallback to default settings
        return CensorSettings()

    def _on_profile_changed(self, profile_name):
        """Handle profile selection change"""
        self.logger.info(f"Profile changed to: {profile_name}")
        # Load the selected profile
        self._load_profile(profile_name)

    def _save_current_profile(self):
        """Save current settings as a new profile"""
        from PyQt6.QtWidgets import QInputDialog

        profile_name, ok = QInputDialog.getText(
            self, "Save Profile", "Enter profile name:"
        )

        if ok and profile_name:
            # Apply current settings first
            self._apply_settings()

            # Save as profile
            self._save_profile(profile_name)

            # Update profile combo
            if self.profile_combo.findText(profile_name) == -1:
                self.profile_combo.addItem(profile_name)

            self.profile_combo.setCurrentText(profile_name)
            self.update_status(f"Profile '{profile_name}' saved successfully")

    def _delete_profile(self):
        """Delete the selected profile"""
        current_profile = self.profile_combo.currentText()

        if current_profile in ["Default"]:
            self.update_status("Cannot delete default profile")
            return

        from PyQt6.QtWidgets import QMessageBox

        reply = QMessageBox.question(
            self, "Delete Profile",
            f"Are you sure you want to delete profile '{current_profile}'?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            self._remove_profile(current_profile)

            # Remove from combo
            index = self.profile_combo.findText(current_profile)
            if index >= 0:
                self.profile_combo.removeItem(index)

            # Switch to default
            self.profile_combo.setCurrentText("Default")
            self.update_status(f"Profile '{current_profile}' deleted")

    def _export_profile(self):
        """Export current profile to file"""
        current_profile = self.profile_combo.currentText()

        file_path, _ = QFileDialog.getSaveFileName(
            self, "Export Profile", f"{current_profile}.json",
            "JSON Files (*.json)"
        )

        if file_path:
            try:
                # Apply current settings first
                self._apply_settings()

                # Export profile data
                profile_data = self._get_profile_data(current_profile)

                import json
                with open(file_path, 'w') as f:
                    json.dump(profile_data, f, indent=2)

                self.update_status(f"Profile exported to {file_path}")

            except Exception as e:
                self.update_status(f"Export failed: {str(e)}")

    def _import_profile(self):
        """Import profile from file"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Import Profile", "", "JSON Files (*.json)"
        )

        if file_path:
            try:
                import json
                import os

                with open(file_path, 'r') as f:
                    profile_data = json.load(f)

                # Get profile name from filename or ask user
                profile_name = os.path.splitext(os.path.basename(file_path))[0]

                from PyQt6.QtWidgets import QInputDialog
                profile_name, ok = QInputDialog.getText(
                    self, "Import Profile", "Profile name:", text=profile_name
                )

                if ok and profile_name:
                    self._import_profile_data(profile_name, profile_data)

                    # Update profile combo
                    if self.profile_combo.findText(profile_name) == -1:
                        self.profile_combo.addItem(profile_name)

                    self.profile_combo.setCurrentText(profile_name)
                    self.update_status(f"Profile '{profile_name}' imported successfully")

            except Exception as e:
                self.update_status(f"Import failed: {str(e)}")

    def _apply_preset(self, preset_type):
        """Apply a predefined preset"""
        from src.config.settings import CensorMethod, BodyPart

        presets = {
            "minimal": {
                "method": CensorMethod.BLUR_LIGHT,
                "intensity": 0.3,
                "opacity": 0.5
            },
            "moderate": {
                "method": CensorMethod.BLUR,
                "intensity": 0.7,
                "opacity": 0.8
            },
            "maximum": {
                "method": CensorMethod.BLACK_BAR,
                "intensity": 1.0,
                "opacity": 1.0
            },
            "artistic": {
                "method": CensorMethod.MOSAIC,
                "intensity": 0.8,
                "opacity": 0.9
            }
        }

        if preset_type in presets and hasattr(self, 'censoring_controls'):
            preset = presets[preset_type]

            for body_part in BodyPart:
                if body_part in self.censoring_controls:
                    controls = self.censoring_controls[body_part]

                    # Set method
                    if 'method_combo' in controls:
                        method_combo = controls['method_combo']
                        for i in range(method_combo.count()):
                            if method_combo.itemData(i) == preset["method"]:
                                method_combo.setCurrentIndex(i)
                                break

                    # Set intensity
                    if 'intensity_slider' in controls:
                        controls['intensity_slider'].setValue(int(preset["intensity"] * 100))
                        controls['intensity_label'].setText(f"{preset['intensity']:.2f}")

                    # Set opacity
                    if 'opacity_slider' in controls:
                        controls['opacity_slider'].setValue(int(preset["opacity"] * 100))
                        controls['opacity_label'].setText(f"{preset['opacity']:.2f}")

                    # Update control states
                    if 'method_combo' in controls:
                        self._on_method_changed(body_part, controls['method_combo'].currentIndex())

            self.update_status(f"Applied {preset_type} preset")

    def _load_profile(self, profile_name):
        """Load a specific profile"""
        # This would load from a profiles storage system
        # For now, just update the description
        descriptions = {
            "Default": "Standard censoring settings with balanced privacy and performance.",
            "Work Safe": "Minimal censoring optimized for work environments.",
            "Maximum Privacy": "Heavy censoring for maximum privacy protection.",
            "Custom": "User-defined custom settings."
        }

        if profile_name in descriptions:
            self.profile_description.setText(descriptions[profile_name])

    def _save_profile(self, profile_name):
        """Save current settings as a profile"""
        # This would save to a profiles storage system
        self.logger.info(f"Saving profile: {profile_name}")

    def _remove_profile(self, profile_name):
        """Remove a profile"""
        # This would remove from profiles storage system
        self.logger.info(f"Removing profile: {profile_name}")

    def _get_profile_data(self, profile_name):
        """Get profile data for export"""
        # This would get profile data from storage
        return {
            "name": profile_name,
            "description": self.profile_description.toPlainText(),
            "settings": "profile_settings_data"
        }

    def _import_profile_data(self, profile_name, profile_data):
        """Import profile data"""
        # This would import profile data to storage
        self.logger.info(f"Importing profile: {profile_name}")
        if "description" in profile_data:
            self.profile_description.setText(profile_data["description"])
    
    def _load_settings(self):
        """Load current settings into the UI"""
        settings = self.controller.settings
        
        # General tab
        self.enabled_checkbox.setChecked(settings.enabled)
        
        from src.config.settings import BodyPart
        self.breasts_checkbox.setChecked(settings.get_body_part_setting(BodyPart.BREASTS).enabled)
        self.genitals_checkbox.setChecked(settings.get_body_part_setting(BodyPart.GENITALS).enabled)
        self.buttocks_checkbox.setChecked(settings.get_body_part_setting(BodyPart.BUTTOCKS).enabled)
        
        # Detection tab
        self.confidence_slider.setValue(int(settings.detection.confidence_threshold * 100))
        self.frame_skip_spinbox.setValue(settings.detection.frame_skip)
        self.gpu_checkbox.setChecked(settings.detection.gpu_acceleration)
        self.roi_checkbox.setChecked(settings.detection.roi_enabled)
        
        # Performance tab
        self.fps_spinbox.setValue(settings.performance.max_fps)
        self.thread_spinbox.setValue(settings.performance.thread_count)
        self.memory_spinbox.setValue(settings.performance.memory_limit_mb)
        
        # Hotkeys tab - Basic controls
        self.hotkey_inputs['toggle_censoring'].setText(settings.hotkeys.toggle_censoring)
        self.hotkey_inputs['toggle_breasts'].setText(settings.hotkeys.toggle_breasts)
        self.hotkey_inputs['toggle_genitals'].setText(settings.hotkeys.toggle_genitals)
        self.hotkey_inputs['toggle_buttocks'].setText(settings.hotkeys.toggle_buttocks)
        self.hotkey_inputs['emergency_disable'].setText(settings.hotkeys.emergency_disable)

        # Hotkeys tab - Advanced controls
        self.hotkey_inputs['quick_preset_minimal'].setText(settings.hotkeys.quick_preset_minimal)
        self.hotkey_inputs['quick_preset_moderate'].setText(settings.hotkeys.quick_preset_moderate)
        self.hotkey_inputs['quick_preset_maximum'].setText(settings.hotkeys.quick_preset_maximum)
        self.hotkey_inputs['temporary_disable'].setText(settings.hotkeys.temporary_disable)
        self.hotkey_inputs['stealth_mode'].setText(settings.hotkeys.stealth_mode)
        self.hotkey_inputs['increase_intensity'].setText(settings.hotkeys.increase_intensity)
        self.hotkey_inputs['decrease_intensity'].setText(settings.hotkeys.decrease_intensity)

        # Hotkeys tab - Settings
        if hasattr(self, 'hotkeys_enabled_checkbox'):
            self.hotkeys_enabled_checkbox.setChecked(settings.hotkeys.enabled)
        if hasattr(self, 'hotkey_cooldown_spinbox'):
            self.hotkey_cooldown_spinbox.setValue(settings.hotkeys.cooldown_ms)
        if hasattr(self, 'temp_disable_duration_spinbox'):
            self.temp_disable_duration_spinbox.setValue(settings.hotkeys.temporary_disable_duration)

        # Advanced censoring tab
        if hasattr(self, 'censoring_controls'):
            for body_part in BodyPart:
                if body_part in self.censoring_controls:
                    controls = self.censoring_controls[body_part]
                    body_part_settings = settings.get_body_part_setting(body_part)

                    # Load method
                    if 'method_combo' in controls:
                        method_combo = controls['method_combo']
                        for i in range(method_combo.count()):
                            if method_combo.itemData(i) == body_part_settings.method:
                                method_combo.setCurrentIndex(i)
                                break

                    # Load other settings
                    if 'intensity_slider' in controls:
                        controls['intensity_slider'].setValue(int(body_part_settings.intensity * 100))
                        controls['intensity_label'].setText(f"{body_part_settings.intensity:.2f}")

                    if 'opacity_slider' in controls:
                        controls['opacity_slider'].setValue(int(body_part_settings.opacity * 100))
                        controls['opacity_label'].setText(f"{body_part_settings.opacity:.2f}")

                    if 'blur_radius_spinbox' in controls:
                        controls['blur_radius_spinbox'].setValue(body_part_settings.blur_radius)

                    if 'pixel_size_spinbox' in controls:
                        controls['pixel_size_spinbox'].setValue(body_part_settings.pixel_size)

                    if 'shape_combo' in controls:
                        shape_combo = controls['shape_combo']
                        shape_text = body_part_settings.shape.replace('_', ' ').title()
                        index = shape_combo.findText(shape_text)
                        if index >= 0:
                            shape_combo.setCurrentIndex(index)

                    if 'padding_spinbox' in controls:
                        controls['padding_spinbox'].setValue(body_part_settings.padding)

                    # Update control states based on method
                    if 'method_combo' in controls:
                        self._on_method_changed(body_part, controls['method_combo'].currentIndex())
    
    def _apply_settings(self):
        """Apply current UI settings"""
        settings = self.controller.settings
        
        # General settings
        settings.enabled = self.enabled_checkbox.isChecked()
        
        from src.config.settings import BodyPart
        settings.get_body_part_setting(BodyPart.BREASTS).enabled = self.breasts_checkbox.isChecked()
        settings.get_body_part_setting(BodyPart.GENITALS).enabled = self.genitals_checkbox.isChecked()
        settings.get_body_part_setting(BodyPart.BUTTOCKS).enabled = self.buttocks_checkbox.isChecked()
        
        # Detection settings
        settings.detection.confidence_threshold = self.confidence_slider.value() / 100.0
        settings.detection.frame_skip = self.frame_skip_spinbox.value()
        settings.detection.gpu_acceleration = self.gpu_checkbox.isChecked()
        settings.detection.roi_enabled = self.roi_checkbox.isChecked()
        
        # Performance settings
        settings.performance.max_fps = self.fps_spinbox.value()
        settings.performance.thread_count = self.thread_spinbox.value()
        settings.performance.memory_limit_mb = self.memory_spinbox.value()
        
        # Hotkey settings - Basic controls
        settings.hotkeys.toggle_censoring = self.hotkey_inputs['toggle_censoring'].text()
        settings.hotkeys.toggle_breasts = self.hotkey_inputs['toggle_breasts'].text()
        settings.hotkeys.toggle_genitals = self.hotkey_inputs['toggle_genitals'].text()
        settings.hotkeys.toggle_buttocks = self.hotkey_inputs['toggle_buttocks'].text()
        settings.hotkeys.emergency_disable = self.hotkey_inputs['emergency_disable'].text()

        # Hotkey settings - Advanced controls
        settings.hotkeys.quick_preset_minimal = self.hotkey_inputs['quick_preset_minimal'].text()
        settings.hotkeys.quick_preset_moderate = self.hotkey_inputs['quick_preset_moderate'].text()
        settings.hotkeys.quick_preset_maximum = self.hotkey_inputs['quick_preset_maximum'].text()
        settings.hotkeys.temporary_disable = self.hotkey_inputs['temporary_disable'].text()
        settings.hotkeys.stealth_mode = self.hotkey_inputs['stealth_mode'].text()
        settings.hotkeys.increase_intensity = self.hotkey_inputs['increase_intensity'].text()
        settings.hotkeys.decrease_intensity = self.hotkey_inputs['decrease_intensity'].text()

        # Hotkey settings - Configuration
        if hasattr(self, 'hotkeys_enabled_checkbox'):
            settings.hotkeys.enabled = self.hotkeys_enabled_checkbox.isChecked()
        if hasattr(self, 'hotkey_cooldown_spinbox'):
            settings.hotkeys.cooldown_ms = self.hotkey_cooldown_spinbox.value()
        if hasattr(self, 'temp_disable_duration_spinbox'):
            settings.hotkeys.temporary_disable_duration = self.temp_disable_duration_spinbox.value()

        # Advanced censoring settings
        if hasattr(self, 'censoring_controls'):
            for body_part in BodyPart:
                if body_part in self.censoring_controls:
                    controls = self.censoring_controls[body_part]
                    body_part_settings = settings.get_body_part_setting(body_part)

                    # Apply method
                    if 'method_combo' in controls:
                        method_combo = controls['method_combo']
                        body_part_settings.method = method_combo.currentData()

                    # Apply other settings
                    if 'intensity_slider' in controls:
                        body_part_settings.intensity = controls['intensity_slider'].value() / 100.0

                    if 'opacity_slider' in controls:
                        body_part_settings.opacity = controls['opacity_slider'].value() / 100.0

                    if 'blur_radius_spinbox' in controls:
                        body_part_settings.blur_radius = controls['blur_radius_spinbox'].value()

                    if 'pixel_size_spinbox' in controls:
                        body_part_settings.pixel_size = controls['pixel_size_spinbox'].value()

                    if 'shape_combo' in controls:
                        shape_combo = controls['shape_combo']
                        body_part_settings.shape = shape_combo.currentText().lower().replace(' ', '_')

                    if 'padding_spinbox' in controls:
                        body_part_settings.padding = controls['padding_spinbox'].value()

                    # Apply color if set
                    if hasattr(self, '_body_part_colors') and body_part in self._body_part_colors:
                        body_part_settings.color = self._body_part_colors[body_part]

                    # Apply custom image if set
                    if hasattr(self, '_body_part_images') and body_part in self._body_part_images:
                        body_part_settings.custom_image_path = self._body_part_images[body_part]

        # Save settings
        settings.save()
        
        # Emit signal
        self.settings_changed.emit()
        
        self.update_status("Settings applied successfully")
    
    def _ok_clicked(self):
        """Handle OK button click"""
        self._apply_settings()
        self.close()
    
    def _toggle_censoring(self):
        """Toggle censoring on/off"""
        try:
            # Import AppState here to avoid circular imports
            from src.app_controller import AppState

            current_state = self.controller.get_state()
            self.logger.info(f"Current state: {current_state.value}")

            if current_state == AppState.RUNNING:
                self.update_status("Stopping censoring...")
                self.controller.stop_censoring()
            else:
                self.update_status("Starting censoring...")
                self.controller.start_censoring()

        except Exception as e:
            self.logger.error(f"Error toggling censoring: {e}")
            self.update_status(f"Error: {e}")
            # Emit error to controller
            if hasattr(self.controller, 'error_occurred'):
                self.controller.error_occurred.emit(str(e))

    def _refresh_status(self):
        """Refresh the status display"""
        try:
            # Get current status from controller
            status = self.controller.get_status()
            stats = self.controller.performance_stats

            # Update performance labels
            self.capture_fps_label.setText(f"{stats.get('capture_fps', 0):.1f}")
            self.processing_fps_label.setText(f"{stats.get('processing_fps', 0):.1f}")
            self.queue_size_label.setText(str(stats.get('queue_size', 0)))

            # Update advanced performance metrics if available
            if hasattr(self, 'cpu_usage_label'):
                self.cpu_usage_label.setText(f"{stats.get('cpu_usage', 0):.1f}%")
            if hasattr(self, 'memory_usage_label'):
                self.memory_usage_label.setText(f"{stats.get('memory_usage_mb', 0):.1f} MB")
            if hasattr(self, 'cache_hits_label'):
                cache_hits = stats.get('cache_hits', 0)
                cache_misses = stats.get('cache_misses', 0)
                total_cache = cache_hits + cache_misses
                hit_rate = (cache_hits / total_cache * 100) if total_cache > 0 else 0
                self.cache_hits_label.setText(f"{hit_rate:.1f}%")
            if hasattr(self, 'quality_level_label'):
                self.quality_level_label.setText(f"Level {stats.get('current_quality_level', 3)}")
            if hasattr(self, 'roi_regions_label'):
                self.roi_regions_label.setText(str(stats.get('roi_regions', 0)))
            self.dropped_frames_label.setText(str(stats.get('dropped_frames', 0)))

            # Update component status
            components = status.get('components', {})
            for comp_name, label in self.component_labels.items():
                comp_key = comp_name.replace('_', '')
                if comp_key in components:
                    is_ok = components[comp_key]
                    label.setText("✓ OK" if is_ok else "✗ Error")
                    label.setStyleSheet(f"color: {'green' if is_ok else 'red'}; font-weight: bold;")
                else:
                    label.setText("Unknown")
                    label.setStyleSheet("color: gray;")

        except Exception as e:
            self.logger.error(f"Error refreshing status: {e}")

    def _on_state_changed(self, state: str):
        """Handle application state changes"""
        self.state_label.setText(state.title())

        # Update state label color
        color_map = {
            'running': 'green',
            'stopped': 'red',
            'starting': 'orange',
            'stopping': 'orange',
            'error': 'red'
        }
        color = color_map.get(state.lower(), 'gray')
        self.state_label.setStyleSheet(f"font-weight: bold; color: {color};")

        # Update start/stop button
        if state.lower() == 'running':
            self.start_stop_button.setText("Stop Censoring")
        else:
            self.start_stop_button.setText("Start Censoring")

        # Log the state change
        self._add_log_entry(f"State changed to: {state}")

    def _on_error_occurred(self, error_msg: str):
        """Handle error notifications"""
        self._add_log_entry(f"ERROR: {error_msg}")
        self.update_status(f"Error: {error_msg}")

    def _add_log_entry(self, message: str):
        """Add an entry to the log display"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        self.log_display.append(f"[{timestamp}] {message}")

        # Keep only last 50 lines
        text = self.log_display.toPlainText()
        lines = text.split('\n')
        if len(lines) > 50:
            self.log_display.setPlainText('\n'.join(lines[-50:]))

    def update_status(self, status: str):
        """Update the status label"""
        if hasattr(self, 'status_label'):
            self.status_label.setText(status)
        self._add_log_entry(status)
