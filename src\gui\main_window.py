"""
Main Settings Window for Censor App
"""

import logging
from PyQt6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                            QLabel, QCheckBox, QComboBox, QSlider, QPushButton,
                            QGroupBox, QGridLayout, QSpinBox, QLineEdit,
                            QColorDialog, QFileDialog, QTabWidget, QTextEdit)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont, QColor
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from src.app_controller import AppController

class MainWindow(QMainWindow):
    """Main settings window for the application"""
    
    # Signals
    settings_changed = pyqtSignal()
    
    def __init__(self, controller: 'AppController'):
        super().__init__()
        self.controller = controller
        self.logger = logging.getLogger(__name__)
        
        self.setWindowTitle("Censor App - Settings")
        self.setFixedSize(600, 500)
        
        # Create central widget and layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Create tab widget
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # Create tabs
        self._create_general_tab()
        self._create_detection_tab()
        self._create_censoring_tab()
        self._create_performance_tab()
        self._create_hotkeys_tab()
        
        # Status bar
        self.status_label = QLabel("Ready")
        layout.addWidget(self.status_label)
        
        # Buttons
        button_layout = QHBoxLayout()
        
        self.apply_button = QPushButton("Apply")
        self.apply_button.clicked.connect(self._apply_settings)
        
        self.ok_button = QPushButton("OK")
        self.ok_button.clicked.connect(self._ok_clicked)
        
        self.cancel_button = QPushButton("Cancel")
        self.cancel_button.clicked.connect(self.close)
        
        button_layout.addStretch()
        button_layout.addWidget(self.apply_button)
        button_layout.addWidget(self.ok_button)
        button_layout.addWidget(self.cancel_button)
        
        layout.addLayout(button_layout)
        
        # Load current settings
        self._load_settings()
        
        self.logger.info("Main window initialized")
    
    def _create_general_tab(self):
        """Create the general settings tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Main enable/disable
        self.enabled_checkbox = QCheckBox("Enable Censoring")
        layout.addWidget(self.enabled_checkbox)
        
        # Body parts group
        body_parts_group = QGroupBox("Body Parts")
        body_parts_layout = QVBoxLayout(body_parts_group)
        
        self.breasts_checkbox = QCheckBox("Censor Breasts")
        self.genitals_checkbox = QCheckBox("Censor Genitals")
        self.buttocks_checkbox = QCheckBox("Censor Buttocks")
        
        body_parts_layout.addWidget(self.breasts_checkbox)
        body_parts_layout.addWidget(self.genitals_checkbox)
        body_parts_layout.addWidget(self.buttocks_checkbox)
        
        layout.addWidget(body_parts_group)
        layout.addStretch()
        
        self.tab_widget.addTab(tab, "General")
    
    def _create_detection_tab(self):
        """Create the detection settings tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Detection settings group
        detection_group = QGroupBox("Detection Settings")
        detection_layout = QGridLayout(detection_group)
        
        # Confidence threshold
        detection_layout.addWidget(QLabel("Confidence Threshold:"), 0, 0)
        self.confidence_slider = QSlider(Qt.Orientation.Horizontal)
        self.confidence_slider.setRange(10, 100)
        self.confidence_slider.setValue(70)
        self.confidence_label = QLabel("0.70")
        self.confidence_slider.valueChanged.connect(
            lambda v: self.confidence_label.setText(f"{v/100:.2f}")
        )
        detection_layout.addWidget(self.confidence_slider, 0, 1)
        detection_layout.addWidget(self.confidence_label, 0, 2)
        
        # Frame skip
        detection_layout.addWidget(QLabel("Frame Skip:"), 1, 0)
        self.frame_skip_spinbox = QSpinBox()
        self.frame_skip_spinbox.setRange(1, 10)
        self.frame_skip_spinbox.setValue(2)
        detection_layout.addWidget(self.frame_skip_spinbox, 1, 1)
        
        # GPU acceleration
        self.gpu_checkbox = QCheckBox("Enable GPU Acceleration")
        detection_layout.addWidget(self.gpu_checkbox, 2, 0, 1, 2)
        
        # ROI optimization
        self.roi_checkbox = QCheckBox("Enable Region of Interest Optimization")
        detection_layout.addWidget(self.roi_checkbox, 3, 0, 1, 2)
        
        layout.addWidget(detection_group)
        layout.addStretch()
        
        self.tab_widget.addTab(tab, "Detection")
    
    def _create_censoring_tab(self):
        """Create the censoring methods tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Create body part specific settings
        from src.config.settings import BodyPart
        
        self.body_part_widgets = {}
        
        for body_part in BodyPart:
            group = QGroupBox(f"{body_part.value.title()} Settings")
            group_layout = QGridLayout(group)
            
            # Method selection
            group_layout.addWidget(QLabel("Method:"), 0, 0)
            method_combo = QComboBox()
            method_combo.addItems(["Blur", "Pixelate", "Black Bar", "Solid Color", "Custom Image"])
            group_layout.addWidget(method_combo, 0, 1)
            
            # Intensity
            group_layout.addWidget(QLabel("Intensity:"), 1, 0)
            intensity_slider = QSlider(Qt.Orientation.Horizontal)
            intensity_slider.setRange(10, 100)
            intensity_slider.setValue(80)
            intensity_label = QLabel("0.80")
            intensity_slider.valueChanged.connect(
                lambda v, label=intensity_label: label.setText(f"{v/100:.2f}")
            )
            group_layout.addWidget(intensity_slider, 1, 1)
            group_layout.addWidget(intensity_label, 1, 2)
            
            # Color selection
            color_button = QPushButton("Choose Color")
            color_button.clicked.connect(lambda checked, bp=body_part: self._choose_color(bp))
            group_layout.addWidget(color_button, 2, 0)
            
            # Custom image
            image_button = QPushButton("Choose Image")
            image_button.clicked.connect(lambda checked, bp=body_part: self._choose_image(bp))
            group_layout.addWidget(image_button, 2, 1)
            
            self.body_part_widgets[body_part] = {
                'method': method_combo,
                'intensity': intensity_slider,
                'intensity_label': intensity_label,
                'color_button': color_button,
                'image_button': image_button
            }
            
            layout.addWidget(group)
        
        layout.addStretch()
        self.tab_widget.addTab(tab, "Censoring")
    
    def _create_performance_tab(self):
        """Create the performance settings tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Performance settings group
        perf_group = QGroupBox("Performance Settings")
        perf_layout = QGridLayout(perf_group)
        
        # Max FPS
        perf_layout.addWidget(QLabel("Max FPS:"), 0, 0)
        self.fps_spinbox = QSpinBox()
        self.fps_spinbox.setRange(5, 60)
        self.fps_spinbox.setValue(30)
        perf_layout.addWidget(self.fps_spinbox, 0, 1)
        
        # Thread count
        perf_layout.addWidget(QLabel("Thread Count:"), 1, 0)
        self.thread_spinbox = QSpinBox()
        self.thread_spinbox.setRange(1, 16)
        self.thread_spinbox.setValue(4)
        perf_layout.addWidget(self.thread_spinbox, 1, 1)
        
        # Memory limit
        perf_layout.addWidget(QLabel("Memory Limit (MB):"), 2, 0)
        self.memory_spinbox = QSpinBox()
        self.memory_spinbox.setRange(256, 4096)
        self.memory_spinbox.setValue(1024)
        perf_layout.addWidget(self.memory_spinbox, 2, 1)
        
        layout.addWidget(perf_group)
        layout.addStretch()
        
        self.tab_widget.addTab(tab, "Performance")
    
    def _create_hotkeys_tab(self):
        """Create the hotkeys settings tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Hotkeys group
        hotkeys_group = QGroupBox("Hotkey Settings")
        hotkeys_layout = QGridLayout(hotkeys_group)
        
        # Create hotkey input fields
        self.hotkey_inputs = {}
        hotkey_labels = {
            'toggle_censoring': 'Toggle Censoring:',
            'toggle_breasts': 'Toggle Breasts:',
            'toggle_genitals': 'Toggle Genitals:',
            'toggle_buttocks': 'Toggle Buttocks:',
            'emergency_disable': 'Emergency Disable:'
        }
        
        for i, (key, label) in enumerate(hotkey_labels.items()):
            hotkeys_layout.addWidget(QLabel(label), i, 0)
            line_edit = QLineEdit()
            line_edit.setPlaceholderText("Click and press key combination")
            self.hotkey_inputs[key] = line_edit
            hotkeys_layout.addWidget(line_edit, i, 1)
        
        layout.addWidget(hotkeys_group)
        layout.addStretch()
        
        self.tab_widget.addTab(tab, "Hotkeys")
    
    def _choose_color(self, body_part):
        """Open color chooser dialog"""
        color = QColorDialog.getColor()
        if color.isValid():
            button = self.body_part_widgets[body_part]['color_button']
            button.setStyleSheet(f"background-color: {color.name()}")
            button.setText(color.name())
    
    def _choose_image(self, body_part):
        """Open file chooser dialog for custom image"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Choose Custom Image", "", 
            "Image Files (*.png *.jpg *.jpeg *.bmp *.gif)"
        )
        if file_path:
            button = self.body_part_widgets[body_part]['image_button']
            button.setText(f"Image: {file_path.split('/')[-1]}")
            button.setToolTip(file_path)
    
    def _load_settings(self):
        """Load current settings into the UI"""
        settings = self.controller.settings
        
        # General tab
        self.enabled_checkbox.setChecked(settings.enabled)
        
        from src.config.settings import BodyPart
        self.breasts_checkbox.setChecked(settings.get_body_part_setting(BodyPart.BREASTS).enabled)
        self.genitals_checkbox.setChecked(settings.get_body_part_setting(BodyPart.GENITALS).enabled)
        self.buttocks_checkbox.setChecked(settings.get_body_part_setting(BodyPart.BUTTOCKS).enabled)
        
        # Detection tab
        self.confidence_slider.setValue(int(settings.detection.confidence_threshold * 100))
        self.frame_skip_spinbox.setValue(settings.detection.frame_skip)
        self.gpu_checkbox.setChecked(settings.detection.gpu_acceleration)
        self.roi_checkbox.setChecked(settings.detection.roi_enabled)
        
        # Performance tab
        self.fps_spinbox.setValue(settings.performance.max_fps)
        self.thread_spinbox.setValue(settings.performance.thread_count)
        self.memory_spinbox.setValue(settings.performance.memory_limit_mb)
        
        # Hotkeys tab
        self.hotkey_inputs['toggle_censoring'].setText(settings.hotkeys.toggle_censoring)
        self.hotkey_inputs['toggle_breasts'].setText(settings.hotkeys.toggle_breasts)
        self.hotkey_inputs['toggle_genitals'].setText(settings.hotkeys.toggle_genitals)
        self.hotkey_inputs['toggle_buttocks'].setText(settings.hotkeys.toggle_buttocks)
        self.hotkey_inputs['emergency_disable'].setText(settings.hotkeys.emergency_disable)
    
    def _apply_settings(self):
        """Apply current UI settings"""
        settings = self.controller.settings
        
        # General settings
        settings.enabled = self.enabled_checkbox.isChecked()
        
        from src.config.settings import BodyPart
        settings.get_body_part_setting(BodyPart.BREASTS).enabled = self.breasts_checkbox.isChecked()
        settings.get_body_part_setting(BodyPart.GENITALS).enabled = self.genitals_checkbox.isChecked()
        settings.get_body_part_setting(BodyPart.BUTTOCKS).enabled = self.buttocks_checkbox.isChecked()
        
        # Detection settings
        settings.detection.confidence_threshold = self.confidence_slider.value() / 100.0
        settings.detection.frame_skip = self.frame_skip_spinbox.value()
        settings.detection.gpu_acceleration = self.gpu_checkbox.isChecked()
        settings.detection.roi_enabled = self.roi_checkbox.isChecked()
        
        # Performance settings
        settings.performance.max_fps = self.fps_spinbox.value()
        settings.performance.thread_count = self.thread_spinbox.value()
        settings.performance.memory_limit_mb = self.memory_spinbox.value()
        
        # Hotkey settings
        settings.hotkeys.toggle_censoring = self.hotkey_inputs['toggle_censoring'].text()
        settings.hotkeys.toggle_breasts = self.hotkey_inputs['toggle_breasts'].text()
        settings.hotkeys.toggle_genitals = self.hotkey_inputs['toggle_genitals'].text()
        settings.hotkeys.toggle_buttocks = self.hotkey_inputs['toggle_buttocks'].text()
        settings.hotkeys.emergency_disable = self.hotkey_inputs['emergency_disable'].text()
        
        # Save settings
        settings.save()
        
        # Emit signal
        self.settings_changed.emit()
        
        self.update_status("Settings applied successfully")
    
    def _ok_clicked(self):
        """Handle OK button click"""
        self._apply_settings()
        self.close()
    
    def update_status(self, status: str):
        """Update the status label"""
        self.status_label.setText(status)
