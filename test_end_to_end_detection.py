#!/usr/bin/env python3
"""
End-to-End Detection Test Script

This script tests the complete nudity detection and censoring pipeline using real nude images.
It verifies that:
1. Detection libraries are properly installed and functional
2. Images can be loaded and processed
3. Nudity detection works correctly
4. Censoring overlays are applied properly
5. The complete workflow functions end-to-end
"""

import os
import sys
import logging
import time
import numpy as np
from pathlib import Path
from typing import List, Dict, Any
import cv2
from PIL import Image

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from config.settings import Settings, BodyPart, CensorMethod
from detection.nudity_detector import NudityDetector
from overlay.overlay_renderer import OverlayRenderer
from performance.performance_manager import PerformanceManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class EndToEndTester:
    """Comprehensive end-to-end testing for nudity detection and censoring"""
    
    def __init__(self, nudes_folder: str = "nudes"):
        self.nudes_folder = Path(nudes_folder)
        self.results = {}
        self.test_images = []
        
        # Initialize components
        self.settings = Settings()
        self.performance_manager = PerformanceManager()
        self.detector = NudityDetector(performance_manager=self.performance_manager)
        self.overlay_renderer = OverlayRenderer()
        
        logger.info("End-to-end tester initialized")
    
    def discover_test_images(self) -> List[Path]:
        """Discover all test images in the nudes folder"""
        if not self.nudes_folder.exists():
            logger.error(f"Nudes folder not found: {self.nudes_folder}")
            return []
        
        image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp'}
        images = []
        
        for file_path in self.nudes_folder.iterdir():
            if file_path.is_file() and file_path.suffix.lower() in image_extensions:
                images.append(file_path)
        
        logger.info(f"Discovered {len(images)} test images")
        return images
    
    def load_image(self, image_path: Path) -> np.ndarray:
        """Load an image file as numpy array"""
        try:
            # Load with PIL first for better format support
            pil_image = Image.open(image_path)
            # Convert to RGB if needed
            if pil_image.mode != 'RGB':
                pil_image = pil_image.convert('RGB')
            
            # Convert to numpy array (OpenCV format)
            image = np.array(pil_image)
            # Convert RGB to BGR for OpenCV
            image = cv2.cvtColor(image, cv2.COLOR_RGB2BGR)
            
            logger.info(f"Loaded image: {image_path.name} - Shape: {image.shape}")
            return image
            
        except Exception as e:
            logger.error(f"Failed to load image {image_path}: {e}")
            return None
    
    def test_detection_on_image(self, image: np.ndarray, image_name: str) -> Dict[str, Any]:
        """Test detection on a single image"""
        logger.info(f"Testing detection on: {image_name}")
        
        start_time = time.time()
        
        # Run detection
        detection_result = self.detector.detect_nudity(image)
        
        detection_time = time.time() - start_time
        
        # Analyze results
        num_detections = len(detection_result.detections)
        body_parts_detected = set()
        confidence_scores = []
        
        for detection in detection_result.detections:
            body_parts_detected.add(detection.body_part)
            confidence_scores.append(detection.confidence)
        
        avg_confidence = np.mean(confidence_scores) if confidence_scores else 0.0
        max_confidence = max(confidence_scores) if confidence_scores else 0.0
        
        result = {
            'image_name': image_name,
            'image_shape': image.shape,
            'detection_time': detection_time,
            'num_detections': num_detections,
            'body_parts_detected': list(body_parts_detected),
            'avg_confidence': avg_confidence,
            'max_confidence': max_confidence,
            'detections': detection_result.detections
        }
        
        logger.info(f"Detection results for {image_name}:")
        logger.info(f"  - Detections: {num_detections}")
        logger.info(f"  - Body parts: {body_parts_detected}")
        logger.info(f"  - Max confidence: {max_confidence:.3f}")
        logger.info(f"  - Processing time: {detection_time:.3f}s")
        
        return result
    
    def test_censoring_on_image(self, image: np.ndarray, detections: List, image_name: str) -> Dict[str, Any]:
        """Test censoring overlay generation on detected regions"""
        logger.info(f"Testing censoring on: {image_name}")
        
        if not detections:
            logger.info(f"No detections to censor in {image_name}")
            return {'censored_regions': 0, 'success': True}
        
        try:
            # Test different censoring methods
            censor_methods = [CensorMethod.BLUR, CensorMethod.PIXELATE, CensorMethod.BLACK_BAR]
            censoring_results = {}
            
            for method in censor_methods:
                # Update settings for this method
                for body_part in BodyPart:
                    if body_part in self.settings.body_part_settings:
                        self.settings.body_part_settings[body_part].censor_method = method
                
                # Generate overlay
                start_time = time.time()
                overlay_data = self.overlay_renderer._generate_overlay_data(
                    detections, image.shape[:2], self.settings
                )
                censor_time = time.time() - start_time
                
                censoring_results[method.value] = {
                    'processing_time': censor_time,
                    'regions_censored': len([d for d in detections if d.body_part in self.settings.get_enabled_body_parts()]),
                    'success': overlay_data is not None
                }
                
                logger.info(f"  - {method.value}: {censoring_results[method.value]['regions_censored']} regions in {censor_time:.3f}s")
            
            return {
                'censored_regions': len(detections),
                'censoring_methods': censoring_results,
                'success': True
            }
            
        except Exception as e:
            logger.error(f"Censoring failed for {image_name}: {e}")
            return {'censored_regions': 0, 'success': False, 'error': str(e)}
    
    def run_comprehensive_test(self) -> Dict[str, Any]:
        """Run comprehensive end-to-end testing"""
        logger.info("=" * 60)
        logger.info("STARTING COMPREHENSIVE END-TO-END TESTING")
        logger.info("=" * 60)
        
        # Discover test images
        self.test_images = self.discover_test_images()
        if not self.test_images:
            logger.error("No test images found!")
            return {'success': False, 'error': 'No test images found'}
        
        # Test results storage
        detection_results = []
        censoring_results = []
        total_detections = 0
        total_processing_time = 0
        
        # Process each image
        for image_path in self.test_images:
            logger.info(f"\n--- Processing: {image_path.name} ---")
            
            # Load image
            image = self.load_image(image_path)
            if image is None:
                continue
            
            # Test detection
            detection_result = self.test_detection_on_image(image, image_path.name)
            detection_results.append(detection_result)
            
            total_detections += detection_result['num_detections']
            total_processing_time += detection_result['detection_time']
            
            # Test censoring if detections found
            if detection_result['detections']:
                censoring_result = self.test_censoring_on_image(
                    image, detection_result['detections'], image_path.name
                )
                censoring_result['image_name'] = image_path.name
                censoring_results.append(censoring_result)
        
        # Compile final results
        final_results = {
            'success': True,
            'total_images_tested': len(self.test_images),
            'total_detections': total_detections,
            'total_processing_time': total_processing_time,
            'avg_processing_time': total_processing_time / len(self.test_images) if self.test_images else 0,
            'detection_results': detection_results,
            'censoring_results': censoring_results,
            'images_with_detections': len([r for r in detection_results if r['num_detections'] > 0]),
            'body_parts_found': set()
        }
        
        # Aggregate body parts found across all images
        for result in detection_results:
            final_results['body_parts_found'].update(result['body_parts_detected'])
        
        final_results['body_parts_found'] = list(final_results['body_parts_found'])
        
        return final_results
    
    def print_summary(self, results: Dict[str, Any]):
        """Print a comprehensive summary of test results"""
        logger.info("\n" + "=" * 60)
        logger.info("END-TO-END TEST SUMMARY")
        logger.info("=" * 60)
        
        if not results['success']:
            logger.error(f"❌ TESTING FAILED: {results.get('error', 'Unknown error')}")
            return
        
        logger.info(f"✅ TESTING COMPLETED SUCCESSFULLY")
        logger.info(f"📊 Images tested: {results['total_images_tested']}")
        logger.info(f"🔍 Total detections: {results['total_detections']}")
        logger.info(f"📸 Images with detections: {results['images_with_detections']}")
        logger.info(f"⏱️  Average processing time: {results['avg_processing_time']:.3f}s per image")
        logger.info(f"🎯 Body parts detected: {', '.join(results['body_parts_found'])}")
        
        # Detailed per-image results
        logger.info("\n📋 Per-Image Results:")
        for result in results['detection_results']:
            status = "✅" if result['num_detections'] > 0 else "⚪"
            logger.info(f"  {status} {result['image_name']}: {result['num_detections']} detections "
                       f"({result['detection_time']:.3f}s)")
        
        # Censoring results
        if results['censoring_results']:
            logger.info("\n🎨 Censoring Results:")
            for result in results['censoring_results']:
                status = "✅" if result['success'] else "❌"
                logger.info(f"  {status} {result['image_name']}: {result['censored_regions']} regions censored")


def main():
    """Main test execution"""
    try:
        # Create tester
        tester = EndToEndTester()
        
        # Run comprehensive tests
        results = tester.run_comprehensive_test()
        
        # Print summary
        tester.print_summary(results)
        
        # Return success status
        return results['success']
        
    except Exception as e:
        logger.error(f"Test execution failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
