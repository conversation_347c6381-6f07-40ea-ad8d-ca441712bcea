#!/usr/bin/env python3
"""
Test script for the enhanced GUI components
"""

import sys
import os
import logging

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTimer

from src.config.settings import Settings
from src.app_controller import AppController

def test_gui():
    """Test the enhanced GUI functionality"""
    print("Enhanced GUI Test")
    print("=" * 40)
    
    # Create QApplication
    app = QApplication(sys.argv)
    
    try:
        # Test 1: Initialize with GUI
        print("\n1. Testing GUI initialization...")
        settings = Settings()
        controller = AppController(settings, headless=False)  # Enable GUI
        
        print("✓ AppController with GUI initialized")
        
        # Test 2: Check GUI components
        print("\n2. Testing GUI components...")
        
        if controller.system_tray:
            print("✓ System tray component available")
            # Show system tray
            if controller.system_tray.show():
                print("✓ System tray displayed successfully")
            else:
                print("⚠ System tray display failed")
        else:
            print("⚠ System tray component not available")
        
        if controller.main_window:
            print("✓ Main window component available")
            # Show main window for testing
            controller.main_window.show()
            print("✓ Main window displayed")
        else:
            print("⚠ Main window component not available")
        
        # Test 3: Start the application
        print("\n3. Testing application startup...")
        controller.start()
        print("✓ Application started successfully")
        
        # Test 4: Test status updates
        print("\n4. Testing status updates...")
        if controller.main_window:
            controller.main_window._refresh_status()
            print("✓ Status refresh works")
        
        # Test 5: Test signal connections
        print("\n5. Testing signal connections...")
        
        # Emit a test state change
        controller.state_changed.emit("testing")
        print("✓ State change signal emitted")
        
        # Test status change
        controller.status_changed.emit("Test status message")
        print("✓ Status change signal emitted")
        
        print("\n" + "=" * 40)
        print("✅ GUI test completed successfully!")
        print("\nGUI Features verified:")
        print("- System tray integration")
        print("- Main window with status monitoring")
        print("- Real-time performance display")
        print("- Signal/slot connections")
        print("- Component status monitoring")
        print("- Log display functionality")
        
        print("\nGUI is now running. Close the windows to exit.")
        
        # Set up a timer to periodically update status
        update_timer = QTimer()
        update_timer.timeout.connect(lambda: controller.main_window._refresh_status() if controller.main_window else None)
        update_timer.start(2000)  # Update every 2 seconds
        
        # Run the application
        return app.exec()
        
    except Exception as e:
        print(f"\n❌ GUI test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    finally:
        # Cleanup
        try:
            if 'controller' in locals():
                controller.stop_censoring()
        except:
            pass

if __name__ == "__main__":
    # Set up logging
    logging.basicConfig(level=logging.INFO)
    
    # Check if system tray is available
    app = QApplication(sys.argv)
    from PyQt6.QtWidgets import QSystemTrayIcon
    
    if not QSystemTrayIcon.isSystemTrayAvailable():
        print("❌ System tray is not available on this system")
        print("The GUI test requires system tray support")
        sys.exit(1)
    
    app.quit()
    
    # Run the test
    exit_code = test_gui()
    sys.exit(exit_code)
