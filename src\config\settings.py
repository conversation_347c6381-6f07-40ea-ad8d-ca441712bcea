"""
Settings and configuration management for Censor App
"""

import os
import json
import logging
from typing import Dict, Any, List
from dataclasses import dataclass, asdict
from enum import Enum

class CensorMethod(Enum):
    """Available censoring methods"""
    BLUR = "blur"
    PIXELATE = "pixelate"
    BLACK_BAR = "black_bar"
    SOLID_COLOR = "solid_color"
    CUSTOM_IMAGE = "custom_image"

class BodyPart(Enum):
    """Detectable body parts"""
    BREASTS = "breasts"
    GENITALS = "genitals"
    BUTTOCKS = "buttocks"

@dataclass
class CensorSettings:
    """Settings for censoring specific body parts"""
    enabled: bool = True
    method: CensorMethod = CensorMethod.BLUR
    intensity: float = 0.8  # 0.0 to 1.0
    color: str = "#000000"  # For solid color method
    custom_image_path: str = ""

@dataclass
class DetectionSettings:
    """Settings for detection engine"""
    confidence_threshold: float = 0.7
    frame_skip: int = 2  # Process every nth frame
    roi_enabled: bool = True  # Region of interest optimization
    gpu_acceleration: bool = True

@dataclass
class PerformanceSettings:
    """Performance optimization settings"""
    max_fps: int = 30
    max_resolution: tuple = (1920, 1080)
    thread_count: int = 4
    memory_limit_mb: int = 1024

@dataclass
class HotkeySettings:
    """Hotkey configuration"""
    toggle_censoring: str = "ctrl+shift+c"
    toggle_breasts: str = "ctrl+shift+b"
    toggle_genitals: str = "ctrl+shift+g"
    toggle_buttocks: str = "ctrl+shift+u"
    emergency_disable: str = "ctrl+shift+x"

class Settings:
    """Main settings manager"""
    
    def __init__(self, config_file: str = "config.json"):
        self.config_file = config_file
        self.logger = logging.getLogger(__name__)
        
        # Default settings
        self.enabled = False
        self.body_part_settings = {
            BodyPart.BREASTS: CensorSettings(),
            BodyPart.GENITALS: CensorSettings(),
            BodyPart.BUTTOCKS: CensorSettings()
        }
        self.detection = DetectionSettings()
        self.performance = PerformanceSettings()
        self.hotkeys = HotkeySettings()
        
        # Load existing settings
        self.load()
    
    def load(self) -> None:
        """Load settings from config file"""
        if not os.path.exists(self.config_file):
            self.logger.info("Config file not found, using defaults")
            return
        
        try:
            with open(self.config_file, 'r') as f:
                data = json.load(f)
            
            self.enabled = data.get('enabled', False)
            
            # Load body part settings
            body_parts_data = data.get('body_parts', {})
            for body_part in BodyPart:
                if body_part.value in body_parts_data:
                    part_data = body_parts_data[body_part.value]
                    self.body_part_settings[body_part] = CensorSettings(
                        enabled=part_data.get('enabled', True),
                        method=CensorMethod(part_data.get('method', 'blur')),
                        intensity=part_data.get('intensity', 0.8),
                        color=part_data.get('color', '#000000'),
                        custom_image_path=part_data.get('custom_image_path', '')
                    )
            
            # Load detection settings
            detection_data = data.get('detection', {})
            self.detection = DetectionSettings(
                confidence_threshold=detection_data.get('confidence_threshold', 0.7),
                frame_skip=detection_data.get('frame_skip', 2),
                roi_enabled=detection_data.get('roi_enabled', True),
                gpu_acceleration=detection_data.get('gpu_acceleration', True)
            )
            
            # Load performance settings
            perf_data = data.get('performance', {})
            self.performance = PerformanceSettings(
                max_fps=perf_data.get('max_fps', 30),
                max_resolution=tuple(perf_data.get('max_resolution', [1920, 1080])),
                thread_count=perf_data.get('thread_count', 4),
                memory_limit_mb=perf_data.get('memory_limit_mb', 1024)
            )
            
            # Load hotkey settings
            hotkey_data = data.get('hotkeys', {})
            self.hotkeys = HotkeySettings(
                toggle_censoring=hotkey_data.get('toggle_censoring', 'ctrl+shift+c'),
                toggle_breasts=hotkey_data.get('toggle_breasts', 'ctrl+shift+b'),
                toggle_genitals=hotkey_data.get('toggle_genitals', 'ctrl+shift+g'),
                toggle_buttocks=hotkey_data.get('toggle_buttocks', 'ctrl+shift+u'),
                emergency_disable=hotkey_data.get('emergency_disable', 'ctrl+shift+x')
            )
            
            self.logger.info("Settings loaded successfully")
            
        except Exception as e:
            self.logger.error(f"Error loading settings: {e}")
    
    def save(self) -> None:
        """Save current settings to config file"""
        try:
            data = {
                'enabled': self.enabled,
                'body_parts': {
                    body_part.value: asdict(settings)
                    for body_part, settings in self.body_part_settings.items()
                },
                'detection': asdict(self.detection),
                'performance': asdict(self.performance),
                'hotkeys': asdict(self.hotkeys)
            }
            
            # Convert enum values to strings
            for body_part_data in data['body_parts'].values():
                if 'method' in body_part_data:
                    body_part_data['method'] = body_part_data['method'].value
            
            with open(self.config_file, 'w') as f:
                json.dump(data, f, indent=2)
            
            self.logger.info("Settings saved successfully")
            
        except Exception as e:
            self.logger.error(f"Error saving settings: {e}")
    
    def get_body_part_setting(self, body_part: BodyPart) -> CensorSettings:
        """Get settings for a specific body part"""
        return self.body_part_settings.get(body_part, CensorSettings())
    
    def set_body_part_setting(self, body_part: BodyPart, settings: CensorSettings) -> None:
        """Set settings for a specific body part"""
        self.body_part_settings[body_part] = settings
        self.save()
    
    def toggle_body_part(self, body_part: BodyPart) -> bool:
        """Toggle censoring for a specific body part"""
        current = self.body_part_settings[body_part]
        current.enabled = not current.enabled
        self.save()
        return current.enabled
    
    def toggle_censoring(self) -> bool:
        """Toggle overall censoring"""
        self.enabled = not self.enabled
        self.save()
        return self.enabled
