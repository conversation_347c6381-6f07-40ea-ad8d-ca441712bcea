"""
Comprehensive test suite for Multi-Monitor and Resolution Support
Tests enhanced monitor detection, DPI scaling, and multi-monitor functionality
"""

import unittest
import sys
import os
import platform
from unittest.mock import Mock, patch, MagicMock

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from src.capture.screen_capture import ScreenCapture, MonitorInfo, MonitorOrientation, CaptureRegion
from src.overlay.overlay_renderer import OverlayRenderer
from src.app_controller import AppController

class TestMultiMonitorSupport(unittest.TestCase):
    """Test multi-monitor support functionality"""
    
    def setUp(self):
        """Set up test environment"""
        self.screen_capture = None
        self.overlay_renderer = None
        self.app_controller = None
    
    def tearDown(self):
        """Clean up after tests"""
        if self.screen_capture:
            self.screen_capture.stop_capture()
        if self.app_controller:
            self.app_controller.stop()
    
    def test_monitor_info_dataclass(self):
        """Test MonitorInfo dataclass functionality"""
        monitor = MonitorInfo(
            index=0,
            x=0,
            y=0,
            width=1920,
            height=1080,
            name="Test Monitor",
            device_name="DISPLAY1",
            is_primary=True,
            dpi_scale=1.5,
            refresh_rate=144,
            orientation=MonitorOrientation.LANDSCAPE,
            color_depth=32,
            work_area=(0, 0, 1920, 1040),
            physical_size=(510, 287)
        )
        
        # Test properties
        self.assertAlmostEqual(monitor.aspect_ratio, 16/9, places=2)
        self.assertAlmostEqual(monitor.diagonal_inches, 23.8, places=1)
        self.assertGreater(monitor.pixels_per_inch, 90)
        
        print(f"✓ MonitorInfo dataclass: {monitor.name} - {monitor.width}x{monitor.height}")
        print(f"  Aspect ratio: {monitor.aspect_ratio:.2f}")
        print(f"  Diagonal: {monitor.diagonal_inches:.1f} inches")
        print(f"  PPI: {monitor.pixels_per_inch:.1f}")
    
    def test_enhanced_monitor_detection(self):
        """Test enhanced monitor detection"""
        try:
            self.screen_capture = ScreenCapture()
            monitors = self.screen_capture.get_monitors()
            
            self.assertGreater(len(monitors), 0, "Should detect at least one monitor")
            
            for monitor in monitors:
                self.assertIsInstance(monitor, MonitorInfo)
                self.assertGreater(monitor.width, 0)
                self.assertGreater(monitor.height, 0)
                self.assertIsNotNone(monitor.name)
                self.assertGreaterEqual(monitor.dpi_scale, 0.5)
                self.assertLessEqual(monitor.dpi_scale, 4.0)
                
                print(f"✓ Detected monitor: {monitor.name}")
                print(f"  Resolution: {monitor.width}x{monitor.height}")
                print(f"  Position: ({monitor.x}, {monitor.y})")
                print(f"  DPI Scale: {monitor.dpi_scale:.2f}")
                print(f"  Primary: {monitor.is_primary}")
                print(f"  Refresh Rate: {monitor.refresh_rate}Hz")
                print(f"  Orientation: {monitor.orientation.value}")
            
            # Test primary monitor detection
            primary_monitors = [m for m in monitors if m.is_primary]
            self.assertEqual(len(primary_monitors), 1, "Should have exactly one primary monitor")
            
        except Exception as e:
            self.fail(f"Enhanced monitor detection failed: {e}")
    
    def test_dpi_scaling_support(self):
        """Test DPI scaling functionality"""
        try:
            self.screen_capture = ScreenCapture()
            monitors = self.screen_capture.get_monitors()
            
            for monitor in monitors:
                # Test coordinate conversion with DPI scaling
                test_x, test_y = 100, 100
                
                # Convert to global coordinates
                global_x, global_y = self.screen_capture.convert_coordinates_to_global(
                    test_x, test_y, monitor.index
                )
                
                # Convert back to monitor coordinates
                local_x, local_y = self.screen_capture.convert_coordinates_to_monitor(
                    global_x, global_y, monitor.index
                )
                
                self.assertEqual(local_x, test_x)
                self.assertEqual(local_y, test_y)
                
                print(f"✓ DPI scaling test for {monitor.name}: {test_x},{test_y} -> {global_x},{global_y} -> {local_x},{local_y}")
            
        except Exception as e:
            self.fail(f"DPI scaling test failed: {e}")
    
    def test_monitor_specific_settings(self):
        """Test monitor-specific settings"""
        try:
            self.screen_capture = ScreenCapture()
            monitors = self.screen_capture.get_monitors()
            
            if len(monitors) > 0:
                monitor_index = 0
                
                # Test setting and getting monitor-specific settings
                self.screen_capture.set_monitor_specific_setting(monitor_index, "max_resolution", (1280, 720))
                self.screen_capture.set_monitor_specific_setting(monitor_index, "target_fps", 30)
                
                max_res = self.screen_capture.get_monitor_specific_setting(monitor_index, "max_resolution")
                target_fps = self.screen_capture.get_monitor_specific_setting(monitor_index, "target_fps")
                
                self.assertEqual(max_res, (1280, 720))
                self.assertEqual(target_fps, 30)
                
                print(f"✓ Monitor-specific settings test passed")
                print(f"  Monitor {monitor_index}: max_resolution={max_res}, target_fps={target_fps}")
            
        except Exception as e:
            self.fail(f"Monitor-specific settings test failed: {e}")
    
    def test_capture_region_with_dpi(self):
        """Test capture region with DPI awareness"""
        try:
            self.screen_capture = ScreenCapture()
            monitors = self.screen_capture.get_monitors()
            
            if len(monitors) > 0:
                monitor = monitors[0]
                
                # Create DPI-aware capture region
                region = CaptureRegion(
                    x=100,
                    y=100,
                    width=400,
                    height=300,
                    monitor_index=monitor.index,
                    dpi_aware=True
                )
                
                success = self.screen_capture.set_capture_region(region)
                self.assertTrue(success, "Should successfully set capture region")
                
                # Test frame capture with DPI awareness
                frame = self.screen_capture.capture_frame()
                if frame is not None:
                    print(f"✓ DPI-aware capture region test passed")
                    print(f"  Captured frame shape: {frame.shape}")
                else:
                    print("! Frame capture returned None (may be expected in test environment)")
            
        except Exception as e:
            self.fail(f"DPI-aware capture region test failed: {e}")
    
    def test_monitor_change_detection(self):
        """Test monitor configuration change detection"""
        try:
            self.screen_capture = ScreenCapture()
            
            # Test monitor configuration hash calculation
            initial_hash = self.screen_capture.last_monitor_config_hash
            self.assertIsNotNone(initial_hash)
            
            # Test manual monitor refresh
            success = self.screen_capture.refresh_monitors()
            self.assertTrue(success, "Monitor refresh should succeed")
            
            print(f"✓ Monitor change detection test passed")
            print(f"  Initial config hash: {initial_hash[:8]}...")
            
        except Exception as e:
            self.fail(f"Monitor change detection test failed: {e}")
    
    def test_performance_stats_multi_monitor(self):
        """Test performance statistics for multi-monitor setup"""
        try:
            self.screen_capture = ScreenCapture()
            
            # Get performance stats
            stats = self.screen_capture.get_performance_stats()
            
            self.assertIn("monitor_count", stats)
            self.assertIn("monitor_stats", stats)
            self.assertIn("monitor_summary", stats)
            self.assertIn("primary_monitor", stats)
            self.assertIn("dpi_aware", stats)
            
            print(f"✓ Multi-monitor performance stats test passed")
            print(f"  Monitor count: {stats['monitor_count']}")
            print(f"  Primary monitor: {stats['primary_monitor']}")
            print(f"  DPI aware: {stats['dpi_aware']}")
            
            # Print monitor summary
            for monitor_info in stats['monitor_summary']:
                print(f"  Monitor {monitor_info['index']}: {monitor_info['name']} - {monitor_info['resolution']}")
            
        except Exception as e:
            self.fail(f"Multi-monitor performance stats test failed: {e}")
    
    @patch('src.overlay.overlay_renderer.OVERLAY_AVAILABLE', True)
    def test_overlay_multi_monitor_setup(self):
        """Test overlay renderer multi-monitor setup"""
        try:
            # Mock PyQt6 components
            with patch('src.overlay.overlay_renderer.QApplication'), \
                 patch('src.overlay.overlay_renderer.QWidget'), \
                 patch('src.overlay.overlay_renderer.QRect'):
                
                self.screen_capture = ScreenCapture()
                self.overlay_renderer = OverlayRenderer()
                
                monitors = self.screen_capture.get_monitors()
                
                # Test enhanced overlay setup
                success = self.overlay_renderer.setup_monitors_from_monitor_info(monitors)
                self.assertTrue(success, "Overlay setup should succeed")
                
                print(f"✓ Overlay multi-monitor setup test passed")
                print(f"  Setup overlays for {len(monitors)} monitors")
            
        except Exception as e:
            print(f"! Overlay test skipped (PyQt6 not available in test environment): {e}")

def run_multi_monitor_tests():
    """Run all multi-monitor tests"""
    print("=" * 60)
    print("MULTI-MONITOR AND RESOLUTION SUPPORT TESTS")
    print("=" * 60)
    
    # Check if we're on Windows for enhanced testing
    if platform.system() == "Windows":
        print("Running on Windows - Enhanced monitor detection available")
    else:
        print("Running on non-Windows - Using fallback monitor detection")
    
    print()
    
    # Create test suite
    suite = unittest.TestLoader().loadTestsFromTestCase(TestMultiMonitorSupport)
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    print("\n" + "=" * 60)
    if result.wasSuccessful():
        print("✓ ALL MULTI-MONITOR TESTS PASSED!")
    else:
        print("✗ Some multi-monitor tests failed")
        for failure in result.failures:
            print(f"FAILURE: {failure[0]}")
            print(f"  {failure[1]}")
        for error in result.errors:
            print(f"ERROR: {error[0]}")
            print(f"  {error[1]}")
    
    print("=" * 60)
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_multi_monitor_tests()
    sys.exit(0 if success else 1)
