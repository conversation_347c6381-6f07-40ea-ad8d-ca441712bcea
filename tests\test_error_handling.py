"""
Error Handling and Robustness Tests for Censor App
Tests error handling, edge cases, and system robustness
"""

import unittest
import sys
import os
import tempfile
import shutil
import threading
import time
import json
from unittest.mock import Mock, patch, MagicMock

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from src.config.settings import Settings, BodyPart, CensorMethod
from src.capture.screen_capture import ScreenCapture
from src.detection.nudity_detector import NudityDetector
from src.overlay.overlay_renderer import OverlayRenderer

class TestConfigurationErrorHandling(unittest.TestCase):
    """Test error handling in configuration management"""
    
    def setUp(self):
        """Set up test environment"""
        self.temp_dir = tempfile.mkdtemp()
        self.config_file = os.path.join(self.temp_dir, "test_config.json")
    
    def tearDown(self):
        """Clean up test environment"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_invalid_config_file_handling(self):
        """Test handling of invalid configuration files"""
        # Test with non-existent directory
        invalid_path = os.path.join(self.temp_dir, "nonexistent", "config.json")
        
        try:
            settings = Settings(invalid_path)
            # Should create with defaults and handle gracefully
            self.assertIsNotNone(settings)
            print("✓ Invalid config path handled gracefully")
        except Exception as e:
            self.fail(f"Should handle invalid config path gracefully: {e}")
    
    def test_corrupted_config_file_handling(self):
        """Test handling of corrupted configuration files"""
        # Create corrupted JSON file
        with open(self.config_file, 'w') as f:
            f.write('{"invalid": json, "missing": }')
        
        try:
            settings = Settings(self.config_file)
            # Should fall back to defaults
            self.assertIsNotNone(settings)
            self.assertFalse(settings.enabled)  # Default value
            print("✓ Corrupted config file handled gracefully")
        except Exception as e:
            self.fail(f"Should handle corrupted config gracefully: {e}")
    
    def test_partial_config_file_handling(self):
        """Test handling of partial configuration files"""
        # Create partial config file
        partial_config = {"enabled": True}
        with open(self.config_file, 'w') as f:
            json.dump(partial_config, f)
        
        try:
            settings = Settings(self.config_file)
            self.assertTrue(settings.enabled)  # From file
            self.assertEqual(settings.detection.confidence_threshold, 0.7)  # Default
            print("✓ Partial config file handled gracefully")
        except Exception as e:
            self.fail(f"Should handle partial config gracefully: {e}")
    
    def test_readonly_config_file_handling(self):
        """Test handling of read-only configuration files"""
        # Create config file and make it read-only
        settings = Settings(self.config_file)
        settings.save()
        
        try:
            os.chmod(self.config_file, 0o444)  # Read-only
            
            # Try to save settings
            settings.enabled = True
            settings.save()  # Should handle gracefully
            
            print("✓ Read-only config file handled gracefully")
        except Exception as e:
            print(f"! Read-only config handling: {e} (may be expected)")
        finally:
            # Restore permissions for cleanup
            try:
                os.chmod(self.config_file, 0o666)
            except:
                pass

class TestComponentErrorHandling(unittest.TestCase):
    """Test error handling in individual components"""
    
    def test_screen_capture_error_handling(self):
        """Test screen capture error handling"""
        try:
            capture = ScreenCapture()
            
            # Test capture with invalid monitor index
            frame = capture.capture_frame(monitor_index=999)
            # Should return None or handle gracefully
            print(f"✓ Invalid monitor index handled: {frame is None}")
            
            # Test capture with invalid region
            frame = capture.capture_frame(region=(-100, -100, 50, 50))
            # Should handle gracefully
            print(f"✓ Invalid capture region handled: {frame is None}")
            
        except Exception as e:
            print(f"! Screen capture error handling: {e}")
    
    def test_detector_error_handling(self):
        """Test nudity detector error handling"""
        detector = NudityDetector()
        
        # Test detection with None input
        try:
            result = detector.detect_nudity(None)
            print("✓ None input handled gracefully")
        except Exception as e:
            print(f"! None input handling: {e}")
        
        # Test detection with invalid image format
        try:
            import numpy as np
            invalid_image = np.array([1, 2, 3])  # Wrong shape
            result = detector.detect_nudity(invalid_image)
            print("✓ Invalid image format handled gracefully")
        except Exception as e:
            print(f"! Invalid image format handling: {e}")
        
        # Test with invalid confidence threshold
        try:
            detector.set_confidence_threshold(-1.0)
            detector.set_confidence_threshold(2.0)
            print("✓ Invalid confidence threshold handled gracefully")
        except Exception as e:
            print(f"! Invalid confidence threshold handling: {e}")
    
    def test_overlay_error_handling(self):
        """Test overlay renderer error handling"""
        try:
            renderer = OverlayRenderer()
            
            # Test with invalid monitor setup
            result = renderer.setup_monitors([])
            print(f"✓ Empty monitor list handled: {result}")
            
            # Test with invalid detections
            renderer.update_overlays(
                detections=None,
                monitor_index=0,
                frame_shape=(480, 640)
            )
            print("✓ None detections handled gracefully")
            
        except Exception as e:
            print(f"! Overlay error handling: {e}")

class TestResourceExhaustionHandling(unittest.TestCase):
    """Test handling of resource exhaustion scenarios"""
    
    def test_memory_pressure_handling(self):
        """Test handling under memory pressure"""
        try:
            import psutil
            process = psutil.Process()
            initial_memory = process.memory_info().rss / 1024 / 1024
            
            # Create multiple components to increase memory usage
            components = []
            for i in range(10):
                try:
                    capture = ScreenCapture()
                    detector = NudityDetector()
                    renderer = OverlayRenderer()
                    components.append((capture, detector, renderer))
                except Exception as e:
                    print(f"! Memory pressure at component {i}: {e}")
                    break
            
            final_memory = process.memory_info().rss / 1024 / 1024
            memory_increase = final_memory - initial_memory
            
            print(f"✓ Memory pressure test: {len(components)} components created")
            print(f"  Memory increase: {memory_increase:.1f} MB")
            
            # Clean up
            del components
            
        except ImportError:
            self.skipTest("psutil not available")
        except Exception as e:
            print(f"! Memory pressure test error: {e}")
    
    def test_concurrent_access_handling(self):
        """Test handling of concurrent access to components"""
        capture = ScreenCapture()
        detector = NudityDetector()
        
        errors = []
        
        def worker_function(worker_id):
            """Worker function for concurrent testing"""
            try:
                for i in range(10):
                    # Try concurrent operations
                    frame = capture.capture_frame()
                    stats = detector.get_performance_stats()
                    time.sleep(0.01)
            except Exception as e:
                errors.append(f"Worker {worker_id}: {e}")
        
        # Start multiple threads
        threads = []
        for i in range(5):
            thread = threading.Thread(target=worker_function, args=(i,))
            threads.append(thread)
            thread.start()
        
        # Wait for completion
        for thread in threads:
            thread.join(timeout=10)
        
        if errors:
            print(f"! Concurrent access errors: {len(errors)}")
            for error in errors[:3]:  # Show first 3 errors
                print(f"  {error}")
        else:
            print("✓ Concurrent access handled gracefully")
    
    def test_rapid_configuration_changes(self):
        """Test handling of rapid configuration changes"""
        temp_dir = tempfile.mkdtemp()
        config_file = os.path.join(temp_dir, "rapid_config.json")
        
        try:
            settings = Settings(config_file)
            
            # Rapidly change settings
            for i in range(100):
                settings.enabled = not settings.enabled
                settings.detection.confidence_threshold = 0.1 + (i % 9) * 0.1
                
                if i % 10 == 0:
                    try:
                        settings.save()
                    except Exception as e:
                        print(f"! Save error at iteration {i}: {e}")
            
            print("✓ Rapid configuration changes handled")
            
        except Exception as e:
            print(f"! Rapid configuration test error: {e}")
        finally:
            shutil.rmtree(temp_dir, ignore_errors=True)

class TestNetworkErrorHandling(unittest.TestCase):
    """Test handling of network-related errors"""
    
    def test_model_download_failure_handling(self):
        """Test handling of model download failures"""
        detector = NudityDetector()
        
        # Mock network failure
        with patch('nudenet.NudeDetector') as mock_nude_detector:
            mock_nude_detector.side_effect = Exception("Network error")
            
            try:
                result = detector.load_model()
                self.assertFalse(result)
                print("✓ Model download failure handled gracefully")
            except Exception as e:
                print(f"! Model download failure handling: {e}")
    
    def test_offline_operation(self):
        """Test operation in offline mode"""
        detector = NudityDetector()
        
        # Test without loading model
        stats = detector.get_performance_stats()
        self.assertIsInstance(stats, dict)
        self.assertFalse(stats['model_loaded'])
        
        print("✓ Offline operation supported")

class TestEdgeCaseHandling(unittest.TestCase):
    """Test handling of edge cases and unusual inputs"""
    
    def test_extreme_image_sizes(self):
        """Test handling of extreme image sizes"""
        detector = NudityDetector()
        
        try:
            import numpy as np
            
            # Test very small image
            tiny_image = np.random.randint(0, 255, (1, 1, 3), dtype=np.uint8)
            processed = detector.preprocess_frame(tiny_image)
            self.assertIsNotNone(processed)
            print("✓ Tiny image handled")
            
            # Test very large image (if memory allows)
            try:
                large_image = np.random.randint(0, 255, (4000, 4000, 3), dtype=np.uint8)
                processed = detector.preprocess_frame(large_image)
                self.assertIsNotNone(processed)
                print("✓ Large image handled")
            except MemoryError:
                print("! Large image test skipped (insufficient memory)")
            
        except Exception as e:
            print(f"! Extreme image size test error: {e}")
    
    def test_invalid_enum_values(self):
        """Test handling of invalid enum values"""
        temp_dir = tempfile.mkdtemp()
        config_file = os.path.join(temp_dir, "invalid_enum_config.json")
        
        try:
            # Create config with invalid enum values
            invalid_config = {
                "enabled": True,
                "body_parts": {
                    "INVALID_BODY_PART": {
                        "enabled": True,
                        "method": "INVALID_METHOD",
                        "intensity": 0.8
                    }
                }
            }
            
            with open(config_file, 'w') as f:
                json.dump(invalid_config, f)
            
            # Try to load settings
            settings = Settings(config_file)
            self.assertIsNotNone(settings)
            print("✓ Invalid enum values handled gracefully")
            
        except Exception as e:
            print(f"! Invalid enum handling: {e}")
        finally:
            shutil.rmtree(temp_dir, ignore_errors=True)

def run_error_handling_tests():
    """Run all error handling and robustness tests"""
    print("=" * 60)
    print("ERROR HANDLING AND ROBUSTNESS TESTS")
    print("=" * 60)
    
    # Create test suite
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Add test classes
    suite.addTests(loader.loadTestsFromTestCase(TestConfigurationErrorHandling))
    suite.addTests(loader.loadTestsFromTestCase(TestComponentErrorHandling))
    suite.addTests(loader.loadTestsFromTestCase(TestResourceExhaustionHandling))
    suite.addTests(loader.loadTestsFromTestCase(TestNetworkErrorHandling))
    suite.addTests(loader.loadTestsFromTestCase(TestEdgeCaseHandling))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    print("\n" + "=" * 60)
    if result.wasSuccessful():
        print("✓ ALL ERROR HANDLING TESTS PASSED!")
    else:
        print("✗ Some error handling tests failed")
        for failure in result.failures:
            print(f"FAILURE: {failure[0]}")
        for error in result.errors:
            print(f"ERROR: {error[0]}")
    
    print("=" * 60)
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_error_handling_tests()
    sys.exit(0 if success else 1)
