"""
Tests for overlay rendering functionality
"""

import unittest
import sys
import os

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

try:
    from overlay.overlay_renderer import OverlayRenderer, OverlayRegion
    from config.settings import BodyPart, CensorMethod
    from detection.nudity_detector import Detection
    OVERLAY_AVAILABLE = True
except ImportError as e:
    OVERLAY_AVAILABLE = False
    print(f"Overlay modules not available: {e}")

class TestOverlayRenderer(unittest.TestCase):
    """Test overlay rendering functionality"""
    
    def setUp(self):
        """Set up test fixtures"""
        if not OVERLAY_AVAILABLE:
            self.skipTest("Overlay modules not available")
        
        self.renderer = OverlayRenderer()
    
    def test_renderer_initialization(self):
        """Test renderer initialization"""
        self.assertIsNotNone(self.renderer)
        self.assertTrue(self.renderer.enabled)
        self.assertFalse(self.renderer.visible)
        self.assertEqual(len(self.renderer.overlay_windows), 0)
        self.assertEqual(self.renderer.min_region_size, 20)
        self.assertEqual(self.renderer.max_regions_per_monitor, 50)
    
    def test_overlay_region_creation(self):
        """Test overlay region creation"""
        region = OverlayRegion(
            x=100, y=200, width=50, height=75,
            body_part=BodyPart.BREASTS,
            censor_method=CensorMethod.BLUR,
            confidence=0.85
        )
        
        self.assertEqual(region.x, 100)
        self.assertEqual(region.y, 200)
        self.assertEqual(region.width, 50)
        self.assertEqual(region.height, 75)
        self.assertEqual(region.body_part, BodyPart.BREASTS)
        self.assertEqual(region.censor_method, CensorMethod.BLUR)
        self.assertEqual(region.confidence, 0.85)
    
    def test_detections_to_regions_conversion(self):
        """Test conversion of detections to overlay regions"""
        # Create test detections
        detections = [
            Detection(
                body_part=BodyPart.BREASTS,
                confidence=0.9,
                bbox=(0.1, 0.2, 0.3, 0.4),  # normalized coordinates
                raw_class="BREAST"
            ),
            Detection(
                body_part=BodyPart.GENITALS,
                confidence=0.8,
                bbox=(0.5, 0.6, 0.7, 0.8),
                raw_class="GENITALS"
            )
        ]
        
        frame_shape = (480, 640)  # height, width
        censor_settings = {
            BodyPart.BREASTS: CensorMethod.BLUR,
            BodyPart.GENITALS: CensorMethod.BLACK_BAR
        }
        
        # Convert detections to regions
        regions = self.renderer._detections_to_regions(
            detections, frame_shape, censor_settings
        )
        
        self.assertEqual(len(regions), 2)
        
        # Check first region (breasts)
        region1 = regions[0]
        self.assertEqual(region1.body_part, BodyPart.BREASTS)
        self.assertEqual(region1.censor_method, CensorMethod.BLUR)
        self.assertEqual(region1.confidence, 0.9)
        # Check pixel coordinates (0.1 * 640 = 64, 0.2 * 480 = 96, etc.)
        self.assertEqual(region1.x, 64)
        self.assertEqual(region1.y, 96)
        self.assertEqual(region1.width, 128)  # (0.3 - 0.1) * 640
        self.assertEqual(region1.height, 96)  # (0.4 - 0.2) * 480
        
        # Check second region (genitals)
        region2 = regions[1]
        self.assertEqual(region2.body_part, BodyPart.GENITALS)
        self.assertEqual(region2.censor_method, CensorMethod.BLACK_BAR)
        self.assertEqual(region2.confidence, 0.8)
    
    def test_region_filtering_by_size(self):
        """Test filtering of regions by minimum size"""
        # Create test detections with different sizes
        detections = [
            # Large region (should pass)
            Detection(BodyPart.BREASTS, 0.9, (0.1, 0.1, 0.3, 0.3), "BREAST"),
            # Small region (should be filtered out)
            Detection(BodyPart.GENITALS, 0.8, (0.5, 0.5, 0.51, 0.51), "GENITALS"),
            # Medium region (should pass)
            Detection(BodyPart.BUTTOCKS, 0.7, (0.7, 0.7, 0.8, 0.8), "BUTTOCKS")
        ]
        
        frame_shape = (480, 640)
        censor_settings = {part: CensorMethod.BLACK_BAR for part in BodyPart}
        
        # Convert to regions
        regions = self.renderer._detections_to_regions(
            detections, frame_shape, censor_settings
        )
        
        # Filter by size (min_region_size = 20)
        filtered_regions = [
            r for r in regions 
            if r.width >= self.renderer.min_region_size and r.height >= self.renderer.min_region_size
        ]
        
        # Should have 2 regions (large and medium, small filtered out)
        self.assertEqual(len(filtered_regions), 2)
        self.assertEqual(filtered_regions[0].body_part, BodyPart.BREASTS)
        self.assertEqual(filtered_regions[1].body_part, BodyPart.BUTTOCKS)
    
    def test_region_limiting(self):
        """Test limiting of regions per monitor"""
        # Create many detections
        detections = []
        for i in range(60):  # More than max_regions_per_monitor (50)
            detection = Detection(
                body_part=BodyPart.BREASTS,
                confidence=0.9 - (i * 0.01),  # Decreasing confidence
                bbox=(0.1 + i*0.01, 0.1, 0.2 + i*0.01, 0.2),
                raw_class="BREAST"
            )
            detections.append(detection)
        
        frame_shape = (480, 640)
        censor_settings = {BodyPart.BREASTS: CensorMethod.BLUR}
        
        # Convert to regions
        regions = self.renderer._detections_to_regions(
            detections, frame_shape, censor_settings
        )
        
        # Should have all 60 regions initially
        self.assertEqual(len(regions), 60)
        
        # Simulate the limiting logic from update_overlays
        if len(regions) > self.renderer.max_regions_per_monitor:
            regions.sort(key=lambda r: r.confidence, reverse=True)
            regions = regions[:self.renderer.max_regions_per_monitor]
        
        # Should be limited to max_regions_per_monitor
        self.assertEqual(len(regions), self.renderer.max_regions_per_monitor)
        
        # Should be sorted by confidence (highest first)
        for i in range(len(regions) - 1):
            self.assertGreaterEqual(regions[i].confidence, regions[i + 1].confidence)
    
    def test_enable_disable_functionality(self):
        """Test enable/disable functionality"""
        # Initially enabled
        self.assertTrue(self.renderer.enabled)
        
        # Disable
        self.renderer.set_enabled(False)
        self.assertFalse(self.renderer.enabled)
        
        # Enable
        self.renderer.set_enabled(True)
        self.assertTrue(self.renderer.enabled)
    
    def test_status_reporting(self):
        """Test status reporting"""
        status = self.renderer.get_status()
        
        self.assertIsInstance(status, dict)
        self.assertIn('available', status)
        self.assertIn('enabled', status)
        self.assertIn('visible', status)
        self.assertIn('monitor_count', status)
        self.assertIn('min_region_size', status)
        self.assertIn('max_regions_per_monitor', status)
        
        # Check initial values
        self.assertTrue(status['enabled'])
        self.assertFalse(status['visible'])
        self.assertEqual(status['monitor_count'], 0)
        self.assertEqual(status['min_region_size'], 20)
        self.assertEqual(status['max_regions_per_monitor'], 50)

class TestOverlayIntegration(unittest.TestCase):
    """Test overlay integration (requires GUI components)"""
    
    def setUp(self):
        """Set up test fixtures"""
        if not OVERLAY_AVAILABLE:
            self.skipTest("Overlay modules not available")
        
        # Check if PyQt6 is available for GUI tests
        try:
            from PyQt6.QtWidgets import QApplication
            from PyQt6.QtCore import QRect
            
            # Create QApplication if it doesn't exist
            self.app = QApplication.instance()
            if self.app is None:
                self.app = QApplication([])
            
            self.gui_available = True
        except ImportError:
            self.gui_available = False
            self.skipTest("PyQt6 not available for GUI tests")
    
    def test_monitor_setup(self):
        """Test monitor setup with QRect geometries"""
        if not self.gui_available:
            self.skipTest("GUI not available")
        
        from PyQt6.QtCore import QRect
        
        renderer = OverlayRenderer()
        
        # Create test monitor geometries
        monitor_geometries = [
            QRect(0, 0, 1920, 1080),      # Primary monitor
            QRect(1920, 0, 1920, 1080),  # Secondary monitor
        ]
        
        # Setup monitors
        success = renderer.setup_monitors(monitor_geometries)
        
        if success:
            self.assertTrue(success)
            self.assertEqual(len(renderer.overlay_windows), 2)
            
            status = renderer.get_status()
            self.assertEqual(status['monitor_count'], 2)
        else:
            # May fail in headless environment
            self.skipTest("Monitor setup failed (likely headless environment)")

if __name__ == '__main__':
    unittest.main()
