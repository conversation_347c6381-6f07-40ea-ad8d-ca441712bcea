#!/usr/bin/env python3
"""
Censor App - Real-time Nudity Detection and Censoring
Main application entry point
"""

import sys
import os
import logging
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTimer
from src.app_controller import AppController
from src.config.settings import Settings

def setup_logging():
    """Set up logging configuration"""
    log_dir = "logs"
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(os.path.join(log_dir, 'censor_app.log')),
            logging.StreamHandler(sys.stdout)
        ]
    )

def main():
    """Main application entry point"""
    # Set up logging
    setup_logging()
    logger = logging.getLogger(__name__)
    logger.info("Starting Censor App...")
    
    # Create QApplication
    app = QApplication(sys.argv)
    app.setQuitOnLastWindowClosed(False)  # Keep running in system tray
    
    # Load settings
    settings = Settings()
    
    # Create and start the main application controller
    controller = AppController(settings)
    
    # Set up graceful shutdown
    def cleanup():
        logger.info("Shutting down Censor App...")
        controller.stop()
    
    app.aboutToQuit.connect(cleanup)
    
    # Start the application
    controller.start()
    
    # Run the Qt event loop
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
