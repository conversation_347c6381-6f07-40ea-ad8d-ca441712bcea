"""
Image processing utilities for Censor App
"""

import logging
import numpy as np
from PIL import Image, ImageFilter
import cv2
from typing import <PERSON>ple, Optional, List

class ImageProcessor:
    """Utility class for image processing operations"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    @staticmethod
    def resize_frame(frame: np.ndarray, target_size: Tuple[int, int], 
                    maintain_aspect: bool = True) -> np.ndarray:
        """
        Resize frame to target size
        
        Args:
            frame: Input frame as numpy array
            target_size: Target (width, height)
            maintain_aspect: Whether to maintain aspect ratio
        
        Returns:
            Resized frame
        """
        try:
            height, width = frame.shape[:2]
            target_width, target_height = target_size
            
            if maintain_aspect:
                # Calculate scaling factor to fit within target size
                scale_w = target_width / width
                scale_h = target_height / height
                scale = min(scale_w, scale_h)
                
                new_width = int(width * scale)
                new_height = int(height * scale)
            else:
                new_width, new_height = target_width, target_height
            
            # Resize using OpenCV
            resized = cv2.resize(frame, (new_width, new_height), interpolation=cv2.INTER_LANCZOS4)
            
            return resized
            
        except Exception as e:
            logging.getLogger(__name__).error(f"Error resizing frame: {e}")
            return frame
    
    @staticmethod
    def frame_to_pil(frame: np.ndarray) -> Image.Image:
        """Convert numpy frame to PIL Image"""
        try:
            if frame.dtype != np.uint8:
                frame = frame.astype(np.uint8)
            
            # Handle different channel formats
            if len(frame.shape) == 3:
                if frame.shape[2] == 3:  # RGB
                    return Image.fromarray(frame, 'RGB')
                elif frame.shape[2] == 4:  # RGBA
                    return Image.fromarray(frame, 'RGBA')
            elif len(frame.shape) == 2:  # Grayscale
                return Image.fromarray(frame, 'L')
            
            # Default to RGB
            return Image.fromarray(frame, 'RGB')
            
        except Exception as e:
            logging.getLogger(__name__).error(f"Error converting frame to PIL: {e}")
            return None
    
    @staticmethod
    def pil_to_frame(image: Image.Image) -> np.ndarray:
        """Convert PIL Image to numpy frame"""
        try:
            return np.array(image)
        except Exception as e:
            logging.getLogger(__name__).error(f"Error converting PIL to frame: {e}")
            return None
    
    @staticmethod
    def apply_blur(frame: np.ndarray, intensity: float = 0.8,
                  region: Optional[Tuple[int, int, int, int]] = None,
                  blur_radius: int = 15) -> np.ndarray:
        """
        Apply blur effect to frame or region

        Args:
            frame: Input frame
            intensity: Blur intensity (0.0 to 1.0)
            region: Optional region (x, y, width, height) to blur
            blur_radius: Custom blur radius

        Returns:
            Blurred frame
        """
        try:
            # Use custom blur radius if provided, otherwise calculate from intensity
            if blur_radius > 0:
                kernel_size = blur_radius
            else:
                kernel_size = max(3, int(intensity * 50))

            if kernel_size % 2 == 0:  # Ensure odd kernel size
                kernel_size += 1

            if region:
                x, y, w, h = region
                # Ensure region is within frame bounds
                x = max(0, min(x, frame.shape[1] - 1))
                y = max(0, min(y, frame.shape[0] - 1))
                w = min(w, frame.shape[1] - x)
                h = min(h, frame.shape[0] - y)

                if w <= 0 or h <= 0:
                    return frame

                # Extract region
                roi = frame[y:y+h, x:x+w]
                # Apply blur to region
                blurred_roi = cv2.GaussianBlur(roi, (kernel_size, kernel_size), 0)
                # Replace region in original frame
                result = frame.copy()
                result[y:y+h, x:x+w] = blurred_roi
                return result
            else:
                # Blur entire frame
                return cv2.GaussianBlur(frame, (kernel_size, kernel_size), 0)

        except Exception as e:
            logging.getLogger(__name__).error(f"Error applying blur: {e}")
            return frame
    
    @staticmethod
    def apply_pixelation(frame: np.ndarray, intensity: float = 0.8,
                        region: Optional[Tuple[int, int, int, int]] = None,
                        pixel_size: int = 10) -> np.ndarray:
        """
        Apply pixelation effect to frame or region

        Args:
            frame: Input frame
            intensity: Pixelation intensity (0.0 to 1.0)
            region: Optional region (x, y, width, height) to pixelate
            pixel_size: Custom pixel block size

        Returns:
            Pixelated frame
        """
        try:
            # Use custom pixel size if provided, otherwise calculate from intensity
            if pixel_size > 0:
                block_size = pixel_size
            else:
                block_size = max(2, int(intensity * 30))

            if region:
                x, y, w, h = region
                # Ensure region is within frame bounds
                x = max(0, min(x, frame.shape[1] - 1))
                y = max(0, min(y, frame.shape[0] - 1))
                w = min(w, frame.shape[1] - x)
                h = min(h, frame.shape[0] - y)

                if w <= 0 or h <= 0:
                    return frame

                # Extract region
                roi = frame[y:y+h, x:x+w]
                # Apply pixelation to region
                pixelated_roi = ImageProcessor._pixelate_region(roi, block_size)
                # Replace region in original frame
                result = frame.copy()
                result[y:y+h, x:x+w] = pixelated_roi
                return result
            else:
                # Pixelate entire frame
                return ImageProcessor._pixelate_region(frame, block_size)

        except Exception as e:
            logging.getLogger(__name__).error(f"Error applying pixelation: {e}")
            return frame

    @staticmethod
    def apply_mosaic(frame: np.ndarray, intensity: float = 0.8,
                    region: Optional[Tuple[int, int, int, int]] = None) -> np.ndarray:
        """
        Apply mosaic effect to frame or region

        Args:
            frame: Input frame
            intensity: Mosaic intensity (0.0 to 1.0)
            region: Optional region (x, y, width, height) to apply mosaic

        Returns:
            Frame with mosaic effect
        """
        try:
            # Calculate mosaic tile size based on intensity
            tile_size = max(3, int(intensity * 20))

            if region:
                x, y, w, h = region
                # Ensure region is within frame bounds
                x = max(0, min(x, frame.shape[1] - 1))
                y = max(0, min(y, frame.shape[0] - 1))
                w = min(w, frame.shape[1] - x)
                h = min(h, frame.shape[0] - y)

                if w <= 0 or h <= 0:
                    return frame

                # Extract region
                roi = frame[y:y+h, x:x+w]
                # Apply mosaic to region
                mosaic_roi = ImageProcessor._apply_mosaic_effect(roi, tile_size)
                # Replace region in original frame
                result = frame.copy()
                result[y:y+h, x:x+w] = mosaic_roi
                return result
            else:
                # Apply mosaic to entire frame
                return ImageProcessor._apply_mosaic_effect(frame, tile_size)

        except Exception as e:
            logging.getLogger(__name__).error(f"Error applying mosaic: {e}")
            return frame

    @staticmethod
    def apply_noise(frame: np.ndarray, intensity: float = 0.8,
                   region: Optional[Tuple[int, int, int, int]] = None) -> np.ndarray:
        """
        Apply noise effect to frame or region

        Args:
            frame: Input frame
            intensity: Noise intensity (0.0 to 1.0)
            region: Optional region (x, y, width, height) to apply noise

        Returns:
            Frame with noise effect
        """
        try:
            if region:
                x, y, w, h = region
                # Ensure region is within frame bounds
                x = max(0, min(x, frame.shape[1] - 1))
                y = max(0, min(y, frame.shape[0] - 1))
                w = min(w, frame.shape[1] - x)
                h = min(h, frame.shape[0] - y)

                if w <= 0 or h <= 0:
                    return frame

                # Extract region
                roi = frame[y:y+h, x:x+w]
                # Apply noise to region
                noisy_roi = ImageProcessor._apply_noise_effect(roi, intensity)
                # Replace region in original frame
                result = frame.copy()
                result[y:y+h, x:x+w] = noisy_roi
                return result
            else:
                # Apply noise to entire frame
                return ImageProcessor._apply_noise_effect(frame, intensity)

        except Exception as e:
            logging.getLogger(__name__).error(f"Error applying noise: {e}")
            return frame

    @staticmethod
    def apply_swirl(frame: np.ndarray, intensity: float = 0.8,
                   region: Optional[Tuple[int, int, int, int]] = None) -> np.ndarray:
        """
        Apply swirl effect to frame or region

        Args:
            frame: Input frame
            intensity: Swirl intensity (0.0 to 1.0)
            region: Optional region (x, y, width, height) to apply swirl

        Returns:
            Frame with swirl effect
        """
        try:
            if region:
                x, y, w, h = region
                # Ensure region is within frame bounds
                x = max(0, min(x, frame.shape[1] - 1))
                y = max(0, min(y, frame.shape[0] - 1))
                w = min(w, frame.shape[1] - x)
                h = min(h, frame.shape[0] - y)

                if w <= 0 or h <= 0:
                    return frame

                # Extract region
                roi = frame[y:y+h, x:x+w]
                # Apply swirl to region
                swirl_roi = ImageProcessor._apply_swirl_effect(roi, intensity)
                # Replace region in original frame
                result = frame.copy()
                result[y:y+h, x:x+w] = swirl_roi
                return result
            else:
                # Apply swirl to entire frame
                return ImageProcessor._apply_swirl_effect(frame, intensity)

        except Exception as e:
            logging.getLogger(__name__).error(f"Error applying swirl: {e}")
            return frame

    @staticmethod
    def apply_solid_color(frame: np.ndarray, color: Tuple[int, int, int] = (0, 0, 0),
                         opacity: float = 0.8,
                         region: Optional[Tuple[int, int, int, int]] = None) -> np.ndarray:
        """
        Apply solid color overlay to frame or region

        Args:
            frame: Input frame
            color: RGB color tuple
            opacity: Overlay opacity (0.0 to 1.0)
            region: Optional region (x, y, width, height) to apply color

        Returns:
            Frame with color overlay
        """
        try:
            if region:
                x, y, w, h = region
                # Ensure region is within frame bounds
                x = max(0, min(x, frame.shape[1] - 1))
                y = max(0, min(y, frame.shape[0] - 1))
                w = min(w, frame.shape[1] - x)
                h = min(h, frame.shape[0] - y)

                if w <= 0 or h <= 0:
                    return frame

                # Create color overlay
                result = frame.copy()
                overlay = np.full((h, w, 3), color, dtype=np.uint8)

                # Blend with original region
                result[y:y+h, x:x+w] = cv2.addWeighted(
                    result[y:y+h, x:x+w], 1 - opacity,
                    overlay, opacity, 0
                )
                return result
            else:
                # Apply color to entire frame
                overlay = np.full_like(frame, color)
                return cv2.addWeighted(frame, 1 - opacity, overlay, opacity, 0)

        except Exception as e:
            logging.getLogger(__name__).error(f"Error applying solid color: {e}")
            return frame

    @staticmethod
    def _apply_mosaic_effect(frame: np.ndarray, tile_size: int) -> np.ndarray:
        """Apply mosaic effect by averaging color blocks"""
        try:
            h, w = frame.shape[:2]
            result = frame.copy()

            for y in range(0, h, tile_size):
                for x in range(0, w, tile_size):
                    # Get tile boundaries
                    y_end = min(y + tile_size, h)
                    x_end = min(x + tile_size, w)

                    # Calculate average color for this tile
                    tile = frame[y:y_end, x:x_end]
                    avg_color = np.mean(tile, axis=(0, 1)).astype(np.uint8)

                    # Fill tile with average color
                    result[y:y_end, x:x_end] = avg_color

            return result
        except Exception as e:
            logging.getLogger(__name__).error(f"Error in mosaic effect: {e}")
            return frame

    @staticmethod
    def _apply_noise_effect(frame: np.ndarray, intensity: float) -> np.ndarray:
        """Apply random noise effect"""
        try:
            # Generate random noise
            noise = np.random.randint(0, int(255 * intensity), frame.shape, dtype=np.uint8)

            # Blend noise with original frame
            result = cv2.addWeighted(frame, 0.7, noise, 0.3, 0)

            return result
        except Exception as e:
            logging.getLogger(__name__).error(f"Error in noise effect: {e}")
            return frame

    @staticmethod
    def _apply_swirl_effect(frame: np.ndarray, intensity: float) -> np.ndarray:
        """Apply swirl distortion effect"""
        try:
            h, w = frame.shape[:2]
            center_x, center_y = w // 2, h // 2

            # Create coordinate grids
            y, x = np.ogrid[:h, :w]

            # Calculate distance from center
            dx = x - center_x
            dy = y - center_y
            distance = np.sqrt(dx**2 + dy**2)

            # Calculate swirl angle based on distance and intensity
            max_distance = min(w, h) // 2
            angle = intensity * np.pi * (1 - distance / max_distance)

            # Apply rotation
            cos_angle = np.cos(angle)
            sin_angle = np.sin(angle)

            new_x = center_x + dx * cos_angle - dy * sin_angle
            new_y = center_y + dx * sin_angle + dy * cos_angle

            # Ensure coordinates are within bounds
            new_x = np.clip(new_x, 0, w - 1).astype(np.float32)
            new_y = np.clip(new_y, 0, h - 1).astype(np.float32)

            # Apply remapping
            result = cv2.remap(frame, new_x, new_y, cv2.INTER_LINEAR)

            return result
        except Exception as e:
            logging.getLogger(__name__).error(f"Error in swirl effect: {e}")
            return frame

    @staticmethod
    def apply_custom_image(frame: np.ndarray, image_path: str, opacity: float = 0.8,
                          region: Optional[Tuple[int, int, int, int]] = None) -> np.ndarray:
        """
        Apply custom image overlay to frame or region

        Args:
            frame: Input frame
            image_path: Path to custom image file
            opacity: Overlay opacity (0.0 to 1.0)
            region: Optional region (x, y, width, height) to apply image

        Returns:
            Frame with custom image overlay
        """
        try:
            import os
            if not os.path.exists(image_path):
                logging.getLogger(__name__).warning(f"Custom image not found: {image_path}")
                return frame

            # Load custom image
            custom_img = cv2.imread(image_path)
            if custom_img is None:
                logging.getLogger(__name__).warning(f"Failed to load custom image: {image_path}")
                return frame

            if region:
                x, y, w, h = region
                # Ensure region is within frame bounds
                x = max(0, min(x, frame.shape[1] - 1))
                y = max(0, min(y, frame.shape[0] - 1))
                w = min(w, frame.shape[1] - x)
                h = min(h, frame.shape[0] - y)

                if w <= 0 or h <= 0:
                    return frame

                # Resize custom image to fit region
                resized_img = cv2.resize(custom_img, (w, h))

                # Blend with original region
                result = frame.copy()
                result[y:y+h, x:x+w] = cv2.addWeighted(
                    result[y:y+h, x:x+w], 1 - opacity,
                    resized_img, opacity, 0
                )
                return result
            else:
                # Apply to entire frame
                h, w = frame.shape[:2]
                resized_img = cv2.resize(custom_img, (w, h))
                return cv2.addWeighted(frame, 1 - opacity, resized_img, opacity, 0)

        except Exception as e:
            logging.getLogger(__name__).error(f"Error applying custom image: {e}")
            return frame
    
    @staticmethod
    def _pixelate_region(region: np.ndarray, pixel_size: int) -> np.ndarray:
        """Apply pixelation to a region"""
        try:
            height, width = region.shape[:2]
            
            # Resize down
            small_height = max(1, height // pixel_size)
            small_width = max(1, width // pixel_size)
            small = cv2.resize(region, (small_width, small_height), interpolation=cv2.INTER_LINEAR)
            
            # Resize back up
            pixelated = cv2.resize(small, (width, height), interpolation=cv2.INTER_NEAREST)
            
            return pixelated
            
        except Exception as e:
            logging.getLogger(__name__).error(f"Error in pixelate region: {e}")
            return region
    

    
    @staticmethod
    def apply_black_bar(frame: np.ndarray, 
                       region: Optional[Tuple[int, int, int, int]] = None) -> np.ndarray:
        """
        Apply black bar censoring
        
        Args:
            frame: Input frame
            region: Optional region (x, y, width, height) to censor
        
        Returns:
            Frame with black bar
        """
        return ImageProcessor.apply_solid_color(frame, (0, 0, 0), region)
    
    @staticmethod
    def overlay_image(frame: np.ndarray, overlay: np.ndarray,
                     position: Tuple[int, int], alpha: float = 1.0) -> np.ndarray:
        """
        Overlay an image onto the frame
        
        Args:
            frame: Base frame
            overlay: Overlay image
            position: (x, y) position for overlay
            alpha: Transparency (0.0 to 1.0)
        
        Returns:
            Frame with overlay applied
        """
        try:
            x, y = position
            h, w = overlay.shape[:2]
            frame_h, frame_w = frame.shape[:2]
            
            # Ensure overlay fits within frame
            if x + w > frame_w:
                w = frame_w - x
                overlay = overlay[:, :w]
            if y + h > frame_h:
                h = frame_h - y
                overlay = overlay[:h, :]
            
            if x < 0 or y < 0 or w <= 0 or h <= 0:
                return frame
            
            # Apply overlay with alpha blending
            result = frame.copy()
            roi = result[y:y+h, x:x+w]
            
            if alpha >= 1.0:
                result[y:y+h, x:x+w] = overlay
            else:
                blended = cv2.addWeighted(roi, 1-alpha, overlay, alpha, 0)
                result[y:y+h, x:x+w] = blended
            
            return result
            
        except Exception as e:
            logging.getLogger(__name__).error(f"Error overlaying image: {e}")
            return frame
    
    @staticmethod
    def calculate_region_bounds(frame_shape: Tuple[int, int], 
                              detection_box: Tuple[float, float, float, float],
                              padding: float = 0.1) -> Tuple[int, int, int, int]:
        """
        Calculate pixel bounds for a detection region
        
        Args:
            frame_shape: (height, width) of the frame
            detection_box: (x1, y1, x2, y2) normalized coordinates (0.0 to 1.0)
            padding: Additional padding around detection
        
        Returns:
            (x, y, width, height) in pixel coordinates
        """
        try:
            height, width = frame_shape[:2]
            x1, y1, x2, y2 = detection_box
            
            # Convert to pixel coordinates
            px1 = int(x1 * width)
            py1 = int(y1 * height)
            px2 = int(x2 * width)
            py2 = int(y2 * height)
            
            # Add padding
            pad_w = int(padding * (px2 - px1))
            pad_h = int(padding * (py2 - py1))
            
            px1 = max(0, px1 - pad_w)
            py1 = max(0, py1 - pad_h)
            px2 = min(width, px2 + pad_w)
            py2 = min(height, py2 + pad_h)
            
            # Return as (x, y, width, height)
            return px1, py1, px2 - px1, py2 - py1
            
        except Exception as e:
            logging.getLogger(__name__).error(f"Error calculating region bounds: {e}")
            return 0, 0, 0, 0
