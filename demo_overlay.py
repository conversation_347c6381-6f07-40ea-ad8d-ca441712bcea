#!/usr/bin/env python3
"""
Demo script for overlay rendering functionality
Run this after installing dependencies to test overlay system
"""

import sys
import os
import time

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_overlay_basic():
    """Test basic overlay functionality"""
    try:
        from overlay.overlay_renderer import OverlayRenderer, OverlayRegion
        from config.settings import BodyPart, CensorMethod
        from detection.nudity_detector import Detection
        
        print("Overlay Rendering System Demo")
        print("=" * 30)
        
        # Create overlay renderer
        print("Initializing overlay renderer...")
        renderer = OverlayRenderer()
        
        # Show initial status
        status = renderer.get_status()
        print(f"✓ Overlay renderer initialized")
        print(f"  Available: {status['available']}")
        print(f"  Enabled: {status['enabled']}")
        print(f"  Monitor count: {status['monitor_count']}")
        print(f"  Min region size: {status['min_region_size']}")
        print(f"  Max regions per monitor: {status['max_regions_per_monitor']}")
        
        # Test overlay region creation
        print("\nTesting overlay region creation...")
        test_regions = [
            OverlayRegion(100, 100, 200, 150, BodyPart.BREASTS, CensorMethod.BLUR, 0.9),
            OverlayRegion(400, 200, 100, 100, BodyPart.GENITALS, CensorMethod.BLACK_BAR, 0.8),
            OverlayRegion(600, 300, 150, 120, BodyPart.BUTTOCKS, CensorMethod.PIXELATE, 0.7),
        ]
        
        for i, region in enumerate(test_regions):
            print(f"  Region {i+1}: {region.body_part.value} at ({region.x}, {region.y}) "
                  f"size {region.width}x{region.height} method {region.censor_method.value}")
        
        # Test detection to region conversion
        print("\nTesting detection to region conversion...")
        test_detections = [
            Detection(BodyPart.BREASTS, 0.9, (0.1, 0.1, 0.3, 0.4), "BREAST"),
            Detection(BodyPart.GENITALS, 0.8, (0.5, 0.5, 0.7, 0.7), "GENITALS"),
            Detection(BodyPart.BUTTOCKS, 0.7, (0.2, 0.6, 0.4, 0.9), "BUTTOCKS"),
        ]
        
        frame_shape = (1080, 1920)  # height, width
        censor_settings = {
            BodyPart.BREASTS: CensorMethod.BLUR,
            BodyPart.GENITALS: CensorMethod.BLACK_BAR,
            BodyPart.BUTTOCKS: CensorMethod.PIXELATE
        }
        
        regions = renderer._detections_to_regions(test_detections, frame_shape, censor_settings)
        
        print(f"✓ Converted {len(test_detections)} detections to {len(regions)} regions")
        for i, region in enumerate(regions):
            print(f"  Region {i+1}: {region.body_part.value} -> "
                  f"({region.x}, {region.y}) {region.width}x{region.height} "
                  f"confidence {region.confidence:.2f}")
        
        # Test region filtering
        print("\nTesting region filtering...")
        
        # Test size filtering
        small_detections = [
            Detection(BodyPart.BREASTS, 0.9, (0.1, 0.1, 0.11, 0.11), "BREAST"),  # Very small
            Detection(BodyPart.GENITALS, 0.8, (0.5, 0.5, 0.6, 0.6), "GENITALS"),  # Normal size
        ]
        
        small_regions = renderer._detections_to_regions(small_detections, frame_shape, censor_settings)
        filtered_regions = [
            r for r in small_regions 
            if r.width >= renderer.min_region_size and r.height >= renderer.min_region_size
        ]
        
        print(f"  Size filtering: {len(small_regions)} -> {len(filtered_regions)} regions")
        
        # Test confidence-based limiting
        many_detections = []
        for i in range(60):
            detection = Detection(
                BodyPart.BREASTS, 0.9 - (i * 0.01),
                (0.1 + i*0.005, 0.1, 0.2 + i*0.005, 0.2),
                "BREAST"
            )
            many_detections.append(detection)
        
        many_regions = renderer._detections_to_regions(many_detections, frame_shape, censor_settings)
        
        # Apply limiting logic
        if len(many_regions) > renderer.max_regions_per_monitor:
            many_regions.sort(key=lambda r: r.confidence, reverse=True)
            limited_regions = many_regions[:renderer.max_regions_per_monitor]
        else:
            limited_regions = many_regions
        
        print(f"  Confidence limiting: {len(many_regions)} -> {len(limited_regions)} regions")
        
        # Test enable/disable
        print("\nTesting enable/disable functionality...")
        renderer.set_enabled(False)
        print(f"  Disabled: {not renderer.enabled}")
        
        renderer.set_enabled(True)
        print(f"  Enabled: {renderer.enabled}")
        
        print("\n✓ Basic overlay tests completed successfully!")
        return True
        
    except ImportError as e:
        print(f"✗ Missing dependencies: {e}")
        print("Please run 'python setup.py' to install required packages")
        return False
    except Exception as e:
        print(f"✗ Error during overlay demo: {e}")
        return False

def test_overlay_gui():
    """Test overlay with GUI components (requires PyQt6)"""
    try:
        from PyQt6.QtWidgets import QApplication
        from PyQt6.QtCore import QRect, QTimer
        from overlay.overlay_renderer import OverlayRenderer
        from detection.nudity_detector import Detection
        from config.settings import BodyPart, CensorMethod
        
        print("\nGUI Overlay Test")
        print("=" * 16)
        print("⚠️  This test requires a display and may show overlay windows")
        
        response = input("\nProceed with GUI overlay test? (y/N): ")
        if response.lower() != 'y':
            print("Skipping GUI overlay test")
            return True
        
        # Create QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        print("Creating overlay renderer...")
        renderer = OverlayRenderer()
        
        # Setup test monitors
        monitor_geometries = [
            QRect(0, 0, 800, 600),  # Test monitor
        ]
        
        success = renderer.setup_monitors(monitor_geometries)
        if not success:
            print("✗ Failed to setup monitor overlays")
            return False
        
        print(f"✓ Setup overlays for {len(monitor_geometries)} monitors")
        
        # Create test detections
        test_detections = [
            Detection(BodyPart.BREASTS, 0.9, (0.2, 0.2, 0.4, 0.4), "BREAST"),
            Detection(BodyPart.GENITALS, 0.8, (0.6, 0.3, 0.8, 0.5), "GENITALS"),
        ]
        
        censor_settings = {
            BodyPart.BREASTS: CensorMethod.BLUR,
            BodyPart.GENITALS: CensorMethod.BLACK_BAR,
        }
        
        print("Showing overlay windows...")
        
        # Update overlays with test detections
        renderer.update_overlays(
            test_detections,
            monitor_index=0,
            frame_shape=(600, 800),
            censor_settings=censor_settings
        )
        
        # Show overlays
        renderer.show_overlays()
        
        print("✓ Overlay windows displayed")
        print("  You should see overlay regions on your screen")
        print("  Press Enter to hide overlays...")
        
        input()
        
        # Hide overlays
        renderer.hide_overlays()
        print("✓ Overlay windows hidden")
        
        return True
        
    except ImportError as e:
        print(f"✗ PyQt6 not available: {e}")
        print("GUI overlay test requires PyQt6")
        return False
    except Exception as e:
        print(f"✗ GUI overlay test failed: {e}")
        return False

def test_integration():
    """Test integration with app controller"""
    try:
        from app_controller import AppController
        from config.settings import Settings
        
        print("\nIntegration Test")
        print("=" * 16)
        
        # Create settings
        settings = Settings("test_overlay_config.json")
        
        # Create app controller (without GUI)
        print("Creating app controller...")
        controller = AppController(settings, headless=True)
        
        # Check components
        print(f"✓ App controller created")
        print(f"  Screen capture: {controller.screen_capture is not None}")
        print(f"  Detection engine: {controller.detection_engine is not None}")
        print(f"  Overlay renderer: {controller.overlay_renderer is not None}")
        
        if controller.overlay_renderer:
            status = controller.overlay_renderer.get_status()
            print(f"  Overlay available: {status['available']}")
            print(f"  Overlay enabled: {status['enabled']}")
        
        # Get full status
        full_status = controller.get_status()
        print(f"\nFull Status:")
        print(f"  Running: {full_status['running']}")
        print(f"  Components: {full_status['components']}")
        if 'overlay_stats' in full_status:
            print(f"  Overlay stats: {full_status['overlay_stats']}")
        
        # Cleanup
        if os.path.exists("test_overlay_config.json"):
            os.remove("test_overlay_config.json")
        
        print("✓ Integration test completed successfully!")
        return True
        
    except Exception as e:
        print(f"✗ Integration test failed: {e}")
        return False

def main():
    """Run demo"""
    print("Censor App - Overlay Rendering Demo")
    print("=" * 40)
    
    # Run tests
    success = True
    
    if not test_overlay_basic():
        success = False
    
    if not test_overlay_gui():
        success = False
    
    if not test_integration():
        success = False
    
    if success:
        print("\n🎉 All overlay tests passed!")
        print("\nThe overlay rendering system is working correctly.")
        print("Next steps:")
        print("1. Run the full application: python main.py")
        print("2. Or test complete pipeline: python demo_screen_capture.py")
    else:
        print("\n❌ Some tests failed. Please check the errors above.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
