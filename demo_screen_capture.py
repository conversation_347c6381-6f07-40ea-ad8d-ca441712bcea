#!/usr/bin/env python3
"""
Demo script for screen capture functionality
Run this after installing dependencies to test screen capture
"""

import sys
import os
import time

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_screen_capture():
    """Test screen capture functionality"""
    try:
        from capture.screen_capture import ScreenCapture
        from utils.image_utils import ImageProcessor
        import numpy as np
        
        print("Screen Capture Demo")
        print("=" * 30)
        
        # Create screen capture instance
        print("Initializing screen capture...")
        capture = ScreenCapture()
        
        # Show detected monitors
        monitors = capture.get_monitors()
        print(f"Detected {len(monitors)} monitors:")
        for i, monitor in enumerate(monitors):
            print(f"  {i}: {monitor.name} - {monitor.width}x{monitor.height}")
        
        # Test single frame capture
        print("\nCapturing single frame...")
        frame = capture.capture_frame()
        
        if frame is not None:
            print(f"✓ Captured frame: {frame.shape} (height x width x channels)")
            print(f"  Frame type: {frame.dtype}")
            print(f"  Frame size: {frame.nbytes} bytes")
        else:
            print("✗ Failed to capture frame")
            return False
        
        # Test image processing
        print("\nTesting image processing...")
        
        # Test blur
        blurred = ImageProcessor.apply_blur(frame, intensity=0.5)
        print(f"✓ Blur applied: {blurred.shape}")
        
        # Test pixelation
        pixelated = ImageProcessor.apply_pixelation(frame, intensity=0.8)
        print(f"✓ Pixelation applied: {pixelated.shape}")
        
        # Test region processing
        region = (10, 10, 100, 100)  # x, y, width, height
        region_blurred = ImageProcessor.apply_blur(frame, intensity=0.8, region=region)
        print(f"✓ Region blur applied: {region_blurred.shape}")
        
        # Test continuous capture for a few seconds
        print("\nTesting continuous capture for 5 seconds...")
        
        frame_count = 0
        def frame_callback(captured_frame):
            nonlocal frame_count
            frame_count += 1
        
        capture.callback = frame_callback
        capture.set_target_fps(10)  # Lower FPS for demo
        
        if capture.start_capture():
            print("✓ Continuous capture started")
            
            # Run for 5 seconds
            start_time = time.time()
            while time.time() - start_time < 5:
                time.sleep(0.1)
                stats = capture.get_performance_stats()
                if stats['fps'] > 0:
                    print(f"  Current FPS: {stats['fps']}, Frames captured: {frame_count}", end='\r')
            
            capture.stop_capture()
            print(f"\n✓ Captured {frame_count} frames in 5 seconds")
            
        else:
            print("✗ Failed to start continuous capture")
            return False
        
        print("\n✓ Screen capture demo completed successfully!")
        return True
        
    except ImportError as e:
        print(f"✗ Missing dependencies: {e}")
        print("Please run 'python setup.py' to install required packages")
        return False
    except Exception as e:
        print(f"✗ Error during screen capture demo: {e}")
        return False

def test_performance():
    """Test capture performance"""
    try:
        from capture.screen_capture import ScreenCapture
        import time
        
        print("\nPerformance Test")
        print("=" * 20)
        
        capture = ScreenCapture()
        
        # Test different FPS settings
        fps_targets = [5, 10, 15, 30]
        
        for target_fps in fps_targets:
            print(f"\nTesting {target_fps} FPS...")
            capture.set_target_fps(target_fps)
            
            frame_count = 0
            def count_frames(frame):
                nonlocal frame_count
                frame_count += 1
            
            capture.callback = count_frames
            
            if capture.start_capture():
                time.sleep(3)  # Run for 3 seconds
                capture.stop_capture()
                
                actual_fps = frame_count / 3.0
                print(f"  Target: {target_fps} FPS, Actual: {actual_fps:.1f} FPS")
                
                if actual_fps >= target_fps * 0.8:  # Within 80% of target
                    print("  ✓ Performance acceptable")
                else:
                    print("  ⚠ Performance below target")
            else:
                print("  ✗ Failed to start capture")
        
        return True
        
    except Exception as e:
        print(f"✗ Performance test failed: {e}")
        return False

def main():
    """Run demo"""
    print("Censor App - Screen Capture Demo")
    print("=" * 40)
    
    # Check if dependencies are available
    try:
        import numpy
        import mss
        import cv2
        import PIL
        print("✓ All required dependencies are available")
    except ImportError as e:
        print(f"✗ Missing dependencies: {e}")
        print("\nTo install dependencies:")
        print("1. Run: python setup.py")
        print("2. Or manually: pip install -r requirements.txt")
        return False
    
    # Run tests
    success = True
    
    if not test_screen_capture():
        success = False
    
    if not test_performance():
        success = False
    
    if success:
        print("\n🎉 All screen capture tests passed!")
        print("\nThe screen capture system is working correctly.")
        print("You can now run the full application with: python main.py")
    else:
        print("\n❌ Some tests failed. Please check the errors above.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
