"""
Test Infrastructure Validation
Validates that all test files are properly created and can be imported
"""

import sys
import os
import importlib.util

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

def validate_test_file(test_file_path, test_name):
    """Validate a single test file"""
    print(f"\n--- Validating {test_name} ---")
    
    if not os.path.exists(test_file_path):
        print(f"✗ File not found: {test_file_path}")
        return False
    
    print(f"✓ File exists: {os.path.basename(test_file_path)}")
    
    try:
        # Try to import the module
        spec = importlib.util.spec_from_file_location("test_module", test_file_path)
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        
        print(f"✓ Module imports successfully")
        
        # Check for test runner function
        if hasattr(module, 'run_' + test_name.lower().replace(' ', '_').replace('-', '_') + '_tests'):
            print(f"✓ Test runner function found")
        else:
            print(f"! Test runner function not found (this may be normal for some tests)")
        
        # Check for unittest.TestCase classes
        test_classes = []
        for attr_name in dir(module):
            attr = getattr(module, attr_name)
            if (isinstance(attr, type) and 
                hasattr(attr, '__bases__') and 
                any('TestCase' in str(base) for base in attr.__bases__)):
                test_classes.append(attr_name)
        
        if test_classes:
            print(f"✓ Test classes found: {', '.join(test_classes)}")
        else:
            print(f"! No test classes found")
        
        return True
        
    except Exception as e:
        print(f"✗ Import error: {e}")
        return False

def main():
    """Main validation function"""
    print("=" * 60)
    print("TEST INFRASTRUCTURE VALIDATION")
    print("=" * 60)
    
    test_dir = os.path.dirname(__file__)
    
    # Define test files to validate
    test_files = [
        ('test_basic.py', 'Basic Tests'),
        ('test_detection.py', 'Detection Tests'),
        ('test_screen_capture.py', 'Screen Capture Tests'),
        ('test_overlay.py', 'Overlay Tests'),
        ('test_hotkeys.py', 'Hotkey Tests'),
        ('test_performance_optimization.py', 'Performance Optimization Tests'),
        ('test_multi_monitor_support.py', 'Multi-Monitor Support Tests'),
        ('test_integration.py', 'Integration Tests'),
        ('test_windows11_compatibility.py', 'Windows 11 Compatibility Tests'),
        ('test_detection_accuracy.py', 'Detection Accuracy Tests'),
        ('test_error_handling.py', 'Error Handling Tests'),
        ('test_performance_benchmarks.py', 'Performance Benchmarks'),
        ('test_system_integration.py', 'System Integration Tests'),
        ('run_all_tests.py', 'Master Test Runner'),
    ]
    
    results = {}
    
    for test_file, test_name in test_files:
        test_path = os.path.join(test_dir, test_file)
        success = validate_test_file(test_path, test_name)
        results[test_name] = success
    
    # Summary
    print("\n" + "=" * 60)
    print("VALIDATION SUMMARY")
    print("=" * 60)
    
    total_tests = len(results)
    passed_tests = sum(1 for success in results.values() if success)
    failed_tests = total_tests - passed_tests
    
    print(f"Total Test Files: {total_tests}")
    print(f"Valid: {passed_tests}")
    print(f"Invalid: {failed_tests}")
    print(f"Success Rate: {(passed_tests/total_tests*100):.1f}%")
    
    print(f"\nDETAILED RESULTS:")
    for test_name, success in results.items():
        status = "✓ VALID" if success else "✗ INVALID"
        print(f"{status} {test_name}")
    
    print("\n" + "=" * 60)
    if failed_tests == 0:
        print("🎉 ALL TEST FILES ARE VALID!")
        print("The testing infrastructure is properly set up.")
    else:
        print(f"⚠️  {failed_tests} TEST FILE(S) HAVE ISSUES.")
        print("Review the validation results above.")
    print("=" * 60)
    
    return failed_tests == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
