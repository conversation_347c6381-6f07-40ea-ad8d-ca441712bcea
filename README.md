# Censor App - Real-time Nudity Detection and Censoring

A Windows desktop application that provides real-time nudity detection and censoring capabilities for screen content.

## Features

- **Real-time Detection**: Continuously monitors and analyzes the entire Windows desktop screen
- **AI-Powered**: Uses advanced computer vision models to detect nudity and identify specific body parts
- **Customizable Censoring**: Multiple censoring methods including blur, pixelation, black bars, and custom overlays
- **Granular Control**: Toggle censoring on/off for specific body parts independently
- **System Tray Integration**: Runs quietly in the background with easy access controls
- **Multi-Monitor Support**: Works across different display resolutions and multi-monitor setups
- **Hotkey Support**: Quick enable/disable functionality with keyboard shortcuts

## Requirements

- Windows 11 (compatible)
- Python 3.9 or higher
- Minimum 8GB RAM (16GB recommended for optimal performance)
- GPU with CUDA support (optional, for enhanced performance)

## Installation

1. Clone this repository
2. Create a virtual environment:
   ```bash
   python -m venv venv
   venv\Scripts\activate
   ```
3. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

## Usage

1. Run the application:
   ```bash
   python main.py
   ```
2. The app will start in the system tray
3. Right-click the tray icon to access settings and controls
4. Use the hotkeys for quick enable/disable functionality

## Configuration

The application provides extensive customization options:
- Detection sensitivity settings
- Censoring methods and intensity
- Body part specific controls
- Hotkey customization
- Performance optimization settings

## Privacy and Security

This application processes screen content locally on your device. No data is transmitted to external servers. All detection and processing happens on your local machine.

## License

This project is for educational and personal use only. Please ensure compliance with local laws and regulations.
