"""
Tests for screen capture functionality
"""

import unittest
import sys
import os
import time
import numpy as np

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

try:
    from capture.screen_capture import Screen<PERSON>apture, Monitor, CaptureRegion
    from utils.image_utils import ImageProcessor
    MSS_AVAILABLE = True
except ImportError as e:
    MSS_AVAILABLE = False
    print(f"MSS not available: {e}")

class TestScreenCapture(unittest.TestCase):
    """Test screen capture functionality"""
    
    def setUp(self):
        """Set up test fixtures"""
        if not MSS_AVAILABLE:
            self.skipTest("MSS library not available")
        
        self.capture = ScreenCapture()
    
    def tearDown(self):
        """Clean up test fixtures"""
        if hasattr(self, 'capture'):
            self.capture.stop_capture()
    
    def test_monitor_detection(self):
        """Test monitor detection"""
        monitors = self.capture.get_monitors()
        self.assertGreater(len(monitors), 0, "Should detect at least one monitor")
        
        for monitor in monitors:
            self.assertIsInstance(monitor, Monitor)
            self.assertGreater(monitor.width, 0)
            self.assertGreater(monitor.height, 0)
            self.assertIsNotNone(monitor.name)
    
    def test_single_frame_capture(self):
        """Test capturing a single frame"""
        frame = self.capture.capture_frame()
        
        self.assertIsNotNone(frame, "Should capture a frame")
        self.assertIsInstance(frame, np.ndarray, "Frame should be numpy array")
        self.assertEqual(len(frame.shape), 3, "Frame should have 3 dimensions")
        self.assertEqual(frame.shape[2], 3, "Frame should have 3 color channels")
        self.assertGreater(frame.shape[0], 0, "Frame should have height")
        self.assertGreater(frame.shape[1], 0, "Frame should have width")
    
    def test_monitor_selection(self):
        """Test monitor selection"""
        monitors = self.capture.get_monitors()
        if len(monitors) > 0:
            # Test valid monitor selection
            result = self.capture.set_monitor(0)
            self.assertTrue(result, "Should successfully set valid monitor")
            
            # Test invalid monitor selection
            result = self.capture.set_monitor(999)
            self.assertFalse(result, "Should fail to set invalid monitor")
    
    def test_capture_region(self):
        """Test setting capture region"""
        monitors = self.capture.get_monitors()
        if len(monitors) > 0:
            monitor = monitors[0]
            
            # Test valid region
            region = CaptureRegion(
                x=10, y=10, 
                width=min(100, monitor.width - 20), 
                height=min(100, monitor.height - 20),
                monitor_index=0
            )
            
            result = self.capture.set_capture_region(region)
            self.assertTrue(result, "Should successfully set valid region")
            
            # Test invalid region (exceeds bounds)
            invalid_region = CaptureRegion(
                x=monitor.width, y=monitor.height,
                width=100, height=100,
                monitor_index=0
            )
            
            result = self.capture.set_capture_region(invalid_region)
            self.assertFalse(result, "Should fail to set invalid region")
    
    def test_fps_setting(self):
        """Test FPS setting"""
        # Test valid FPS
        self.capture.set_target_fps(15)
        self.assertEqual(self.capture.target_fps, 15)
        
        self.capture.set_target_fps(30)
        self.assertEqual(self.capture.target_fps, 30)
        
        # Test invalid FPS (should not change)
        original_fps = self.capture.target_fps
        self.capture.set_target_fps(0)
        self.assertEqual(self.capture.target_fps, original_fps)
        
        self.capture.set_target_fps(100)
        self.assertEqual(self.capture.target_fps, original_fps)
    
    def test_resolution_setting(self):
        """Test resolution setting"""
        # Test valid resolution
        self.capture.set_max_resolution(1280, 720)
        self.assertEqual(self.capture.max_resolution, (1280, 720))
        
        # Test invalid resolution (should not change)
        original_res = self.capture.max_resolution
        self.capture.set_max_resolution(0, 720)
        self.assertEqual(self.capture.max_resolution, original_res)
        
        self.capture.set_max_resolution(1280, 0)
        self.assertEqual(self.capture.max_resolution, original_res)
    
    def test_performance_stats(self):
        """Test performance statistics"""
        stats = self.capture.get_performance_stats()
        
        self.assertIsInstance(stats, dict)
        self.assertIn('fps', stats)
        self.assertIn('target_fps', stats)
        self.assertIn('monitor_count', stats)
        self.assertIn('running', stats)
        
        self.assertEqual(stats['target_fps'], self.capture.target_fps)
        self.assertEqual(stats['monitor_count'], len(self.capture.monitors))
        self.assertFalse(stats['running'])  # Should not be running initially

class TestImageProcessor(unittest.TestCase):
    """Test image processing utilities"""
    
    def setUp(self):
        """Set up test fixtures"""
        # Create a test image
        self.test_frame = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
    
    def test_resize_frame(self):
        """Test frame resizing"""
        # Test resize with aspect ratio
        resized = ImageProcessor.resize_frame(self.test_frame, (50, 50), maintain_aspect=True)
        self.assertIsInstance(resized, np.ndarray)
        self.assertLessEqual(max(resized.shape[:2]), 50)
        
        # Test resize without aspect ratio
        resized = ImageProcessor.resize_frame(self.test_frame, (50, 75), maintain_aspect=False)
        self.assertEqual(resized.shape[:2], (75, 50))  # Height, Width
    
    def test_blur_application(self):
        """Test blur effect"""
        # Test full frame blur
        blurred = ImageProcessor.apply_blur(self.test_frame, intensity=0.5)
        self.assertEqual(blurred.shape, self.test_frame.shape)
        self.assertIsInstance(blurred, np.ndarray)
        
        # Test region blur
        region = (10, 10, 30, 30)
        blurred_region = ImageProcessor.apply_blur(self.test_frame, intensity=0.5, region=region)
        self.assertEqual(blurred_region.shape, self.test_frame.shape)
    
    def test_pixelation_application(self):
        """Test pixelation effect"""
        # Test full frame pixelation
        pixelated = ImageProcessor.apply_pixelation(self.test_frame, intensity=0.8)
        self.assertEqual(pixelated.shape, self.test_frame.shape)
        self.assertIsInstance(pixelated, np.ndarray)
        
        # Test region pixelation
        region = (10, 10, 30, 30)
        pixelated_region = ImageProcessor.apply_pixelation(self.test_frame, intensity=0.8, region=region)
        self.assertEqual(pixelated_region.shape, self.test_frame.shape)
    
    def test_solid_color_application(self):
        """Test solid color overlay"""
        # Test full frame color
        colored = ImageProcessor.apply_solid_color(self.test_frame, color=(255, 0, 0))
        self.assertEqual(colored.shape, self.test_frame.shape)
        self.assertTrue(np.all(colored == [255, 0, 0]))
        
        # Test region color
        region = (10, 10, 30, 30)
        colored_region = ImageProcessor.apply_solid_color(self.test_frame, color=(0, 255, 0), region=region)
        self.assertEqual(colored_region.shape, self.test_frame.shape)
        
        # Check that the region is colored correctly
        x, y, w, h = region
        region_pixels = colored_region[y:y+h, x:x+w]
        self.assertTrue(np.all(region_pixels == [0, 255, 0]))
    
    def test_black_bar_application(self):
        """Test black bar censoring"""
        region = (10, 10, 30, 30)
        censored = ImageProcessor.apply_black_bar(self.test_frame, region=region)
        self.assertEqual(censored.shape, self.test_frame.shape)
        
        # Check that the region is black
        x, y, w, h = region
        region_pixels = censored[y:y+h, x:x+w]
        self.assertTrue(np.all(region_pixels == [0, 0, 0]))
    
    def test_region_bounds_calculation(self):
        """Test region bounds calculation"""
        frame_shape = (100, 200)  # height, width
        detection_box = (0.1, 0.2, 0.6, 0.8)  # normalized coordinates
        
        x, y, w, h = ImageProcessor.calculate_region_bounds(frame_shape, detection_box, padding=0.0)
        
        # Check bounds are reasonable
        self.assertGreaterEqual(x, 0)
        self.assertGreaterEqual(y, 0)
        self.assertGreater(w, 0)
        self.assertGreater(h, 0)
        self.assertLessEqual(x + w, 200)  # width
        self.assertLessEqual(y + h, 100)  # height

if __name__ == '__main__':
    unittest.main()
