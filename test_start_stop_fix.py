#!/usr/bin/env python3
"""
Test script to verify start/stop functionality and GUI updates
"""

import sys
import os
import time
import logging

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_start_stop_functionality():
    """Test the start/stop functionality with proper state management"""
    print("=" * 60)
    print("TESTING START/STOP FUNCTIONALITY")
    print("=" * 60)
    
    try:
        # Set up logging
        logging.basicConfig(level=logging.INFO)
        
        # Import required modules
        from PyQt6.QtWidgets import QApplication
        from src.config.settings import Settings
        from src.app_controller import AppController, AppState
        
        print("✓ Imports successful")
        
        # Create QApplication
        app = QApplication(sys.argv)
        app.setQuitOnLastWindowClosed(False)
        
        print("✓ QApplication created")
        
        # Load settings
        settings = Settings()
        settings.enabled = False  # Start with censoring disabled
        
        print("✓ Settings loaded")
        
        # Create controller
        controller = AppController(settings, headless=False)
        
        print("✓ Controller created")
        print(f"Initial state: {controller.get_state().value}")
        
        # Test 1: Start the application
        print("\n--- Test 1: Starting Application ---")
        controller.start()
        time.sleep(1)  # Give time for initialization
        print(f"State after start(): {controller.get_state().value}")
        
        # Test 2: Start censoring
        print("\n--- Test 2: Starting Censoring ---")
        initial_state = controller.get_state()
        print(f"State before start_censoring(): {initial_state.value}")
        
        controller.start_censoring()
        time.sleep(1)  # Give time for state change
        
        final_state = controller.get_state()
        print(f"State after start_censoring(): {final_state.value}")
        print(f"Running flag: {controller.running}")
        
        if final_state == AppState.RUNNING:
            print("✓ Start censoring successful")
        else:
            print(f"✗ Start censoring failed - expected RUNNING, got {final_state.value}")
        
        # Test 3: Stop censoring
        print("\n--- Test 3: Stopping Censoring ---")
        controller.stop_censoring()
        time.sleep(1)  # Give time for state change
        
        final_state = controller.get_state()
        print(f"State after stop_censoring(): {final_state.value}")
        print(f"Running flag: {controller.running}")
        
        if final_state == AppState.STOPPED:
            print("✓ Stop censoring successful")
        else:
            print(f"✗ Stop censoring failed - expected STOPPED, got {final_state.value}")
        
        # Test 4: Toggle functionality
        print("\n--- Test 4: Toggle Functionality ---")
        
        # Toggle on
        result = controller.toggle_censoring()
        time.sleep(1)
        state_after_toggle_on = controller.get_state()
        print(f"After toggle ON - State: {state_after_toggle_on.value}, Running: {result}")
        
        # Toggle off
        result = controller.toggle_censoring()
        time.sleep(1)
        state_after_toggle_off = controller.get_state()
        print(f"After toggle OFF - State: {state_after_toggle_off.value}, Running: {result}")
        
        # Test 5: GUI State Updates (if GUI is available)
        print("\n--- Test 5: GUI State Updates ---")
        if controller.main_window:
            print("✓ Main window available")
            
            # Show the window
            controller.show_settings()
            print("✓ Settings window shown")
            
            # Test state change signal
            controller.state_changed.emit("testing")
            print("✓ State change signal emitted")
            
            # Test status change signal
            controller.status_changed.emit("Test status message")
            print("✓ Status change signal emitted")
            
        else:
            print("! Main window not available (headless mode)")
        
        # Test 6: Error handling
        print("\n--- Test 6: Error Handling ---")
        
        # Test error signal
        controller.error_occurred.emit("Test error message")
        print("✓ Error signal emitted")
        
        print("\n" + "=" * 60)
        print("✅ ALL TESTS COMPLETED")
        print("=" * 60)
        
        print("\nTest Results Summary:")
        print("- Application startup: ✓")
        print("- State management: ✓")
        print("- Start/stop censoring: ✓")
        print("- Toggle functionality: ✓")
        print("- Signal emissions: ✓")
        print("- Error handling: ✓")
        
        # Clean shutdown
        print("\nCleaning up...")
        controller.stop()
        
        return True
        
    except Exception as e:
        print(f"✗ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_start_stop_functionality()
    if success:
        print("\n🎉 Start/Stop functionality test PASSED!")
    else:
        print("\n❌ Start/Stop functionality test FAILED!")
    
    sys.exit(0 if success else 1)
