"""
Windows 11 Compatibility Tests for Censor App
Tests Windows 11 specific features and compatibility
"""

import unittest
import sys
import os
import platform
import subprocess
from unittest.mock import patch, MagicMock

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from src.capture.screen_capture import ScreenCapture, MonitorInfo, MonitorOrientation
from src.config.settings import Settings

class TestWindows11Features(unittest.TestCase):
    """Test Windows 11 specific features"""
    
    def setUp(self):
        """Set up test environment"""
        self.is_windows = platform.system() == "Windows"
        self.is_windows11 = self._check_windows11()
        
        if not self.is_windows:
            self.skipTest("Windows-specific tests require Windows OS")
    
    def _check_windows11(self):
        """Check if running on Windows 11"""
        if not self.is_windows:
            return False
        
        try:
            # Check Windows version
            version = platform.version()
            # Windows 11 has build number 22000 or higher
            build_number = int(version.split('.')[-1])
            return build_number >= 22000
        except:
            return False
    
    def test_windows_version_detection(self):
        """Test Windows version detection"""
        print(f"Platform: {platform.system()}")
        print(f"Version: {platform.version()}")
        print(f"Release: {platform.release()}")
        
        if self.is_windows11:
            print("✓ Running on Windows 11")
        else:
            print("! Not running on Windows 11")
        
        self.assertTrue(self.is_windows)
    
    def test_dpi_scaling_support(self):
        """Test DPI scaling support on Windows 11"""
        try:
            screen_capture = ScreenCapture()
            monitors = screen_capture.get_monitors()
            
            dpi_aware_monitors = 0
            for monitor in monitors:
                if hasattr(monitor, 'dpi_scale') and monitor.dpi_scale != 1.0:
                    dpi_aware_monitors += 1
                    print(f"✓ Monitor {monitor.name}: DPI scale {monitor.dpi_scale:.2f}")
            
            if dpi_aware_monitors > 0:
                print(f"✓ DPI scaling detected on {dpi_aware_monitors} monitors")
            else:
                print("! No DPI scaling detected (may be expected)")
            
            # Test should pass regardless of DPI scaling presence
            self.assertTrue(True)
            
        except Exception as e:
            self.fail(f"DPI scaling test failed: {e}")
    
    def test_high_dpi_display_support(self):
        """Test high DPI display support"""
        try:
            screen_capture = ScreenCapture()
            monitors = screen_capture.get_monitors()
            
            high_dpi_monitors = []
            for monitor in monitors:
                # Calculate PPI if physical size is available
                if hasattr(monitor, 'physical_size') and monitor.physical_size != (0, 0):
                    ppi = monitor.pixels_per_inch if hasattr(monitor, 'pixels_per_inch') else 0
                    if ppi > 150:  # Consider >150 PPI as high DPI
                        high_dpi_monitors.append(monitor)
                        print(f"✓ High DPI monitor detected: {monitor.name} ({ppi:.1f} PPI)")
            
            if high_dpi_monitors:
                print(f"✓ {len(high_dpi_monitors)} high DPI monitors detected")
            else:
                print("! No high DPI monitors detected")
            
            # Test coordinate conversion on high DPI
            for monitor in high_dpi_monitors:
                test_x, test_y = 100, 100
                global_x, global_y = screen_capture.convert_coordinates_to_global(
                    test_x, test_y, monitor.index
                )
                local_x, local_y = screen_capture.convert_coordinates_to_monitor(
                    global_x, global_y, monitor.index
                )
                
                self.assertEqual(local_x, test_x)
                self.assertEqual(local_y, test_y)
                print(f"✓ High DPI coordinate conversion test passed for {monitor.name}")
            
        except Exception as e:
            self.fail(f"High DPI display test failed: {e}")
    
    def test_multiple_monitor_orientations(self):
        """Test support for different monitor orientations"""
        try:
            screen_capture = ScreenCapture()
            monitors = screen_capture.get_monitors()
            
            orientations_found = set()
            for monitor in monitors:
                if hasattr(monitor, 'orientation'):
                    orientations_found.add(monitor.orientation)
                    print(f"✓ Monitor {monitor.name}: {monitor.orientation.value if hasattr(monitor.orientation, 'value') else monitor.orientation}")
            
            if len(orientations_found) > 1:
                print(f"✓ Multiple orientations detected: {[str(o) for o in orientations_found]}")
            else:
                print("! Single orientation detected (may be expected)")
            
            # Test should pass regardless of orientation variety
            self.assertTrue(True)
            
        except Exception as e:
            self.fail(f"Monitor orientation test failed: {e}")
    
    def test_windows11_display_settings_integration(self):
        """Test integration with Windows 11 display settings"""
        try:
            screen_capture = ScreenCapture()
            monitors = screen_capture.get_monitors()
            
            # Test refresh rate detection
            high_refresh_monitors = []
            for monitor in monitors:
                if hasattr(monitor, 'refresh_rate') and monitor.refresh_rate > 60:
                    high_refresh_monitors.append(monitor)
                    print(f"✓ High refresh rate monitor: {monitor.name} ({monitor.refresh_rate}Hz)")
            
            if high_refresh_monitors:
                print(f"✓ {len(high_refresh_monitors)} high refresh rate monitors detected")
            
            # Test color depth detection
            for monitor in monitors:
                if hasattr(monitor, 'color_depth'):
                    print(f"✓ Monitor {monitor.name}: {monitor.color_depth}-bit color")
            
            print("✓ Windows 11 display settings integration test passed")
            
        except Exception as e:
            self.fail(f"Display settings integration test failed: {e}")

class TestWindows11SystemIntegration(unittest.TestCase):
    """Test Windows 11 system integration features"""
    
    def setUp(self):
        """Set up test environment"""
        self.is_windows = platform.system() == "Windows"
        if not self.is_windows:
            self.skipTest("Windows-specific tests require Windows OS")
    
    def test_windows_api_integration(self):
        """Test Windows API integration"""
        try:
            # Test pywin32 availability
            import win32api
            import win32con
            import win32gui
            
            print("✓ Windows API libraries available")
            
            # Test basic Windows API calls
            user_name = win32api.GetUserName()
            self.assertIsInstance(user_name, str)
            print(f"✓ Windows API call successful: User = {user_name}")
            
        except ImportError:
            self.skipTest("pywin32 not available")
        except Exception as e:
            self.fail(f"Windows API integration test failed: {e}")
    
    def test_system_tray_compatibility(self):
        """Test system tray compatibility on Windows 11"""
        try:
            # Test pystray availability
            import pystray
            from PIL import Image
            
            # Create a simple test icon
            test_image = Image.new('RGB', (16, 16), color='red')
            
            # Test icon creation (don't actually show it)
            icon = pystray.Icon("test", test_image)
            self.assertIsNotNone(icon)
            
            print("✓ System tray compatibility test passed")
            
        except ImportError:
            self.skipTest("pystray not available")
        except Exception as e:
            self.fail(f"System tray compatibility test failed: {e}")
    
    def test_keyboard_hook_compatibility(self):
        """Test keyboard hook compatibility on Windows 11"""
        try:
            # Test keyboard library availability
            import keyboard
            
            # Test basic keyboard functionality (without actually setting hooks)
            # Just test that the library loads and basic functions are available
            self.assertTrue(hasattr(keyboard, 'add_hotkey'))
            self.assertTrue(hasattr(keyboard, 'remove_hotkey'))
            
            print("✓ Keyboard hook compatibility test passed")
            
        except ImportError:
            self.skipTest("keyboard library not available")
        except Exception as e:
            self.fail(f"Keyboard hook compatibility test failed: {e}")

class TestWindows11Performance(unittest.TestCase):
    """Test performance characteristics on Windows 11"""
    
    def setUp(self):
        """Set up test environment"""
        self.is_windows = platform.system() == "Windows"
        if not self.is_windows:
            self.skipTest("Windows-specific tests require Windows OS")
    
    def test_screen_capture_performance(self):
        """Test screen capture performance on Windows 11"""
        try:
            screen_capture = ScreenCapture()
            
            # Test capture performance
            import time
            start_time = time.time()
            
            successful_captures = 0
            for i in range(10):
                frame = screen_capture.capture_frame()
                if frame is not None:
                    successful_captures += 1
            
            end_time = time.time()
            total_time = end_time - start_time
            
            if successful_captures > 0:
                avg_time_per_frame = total_time / successful_captures
                fps = 1.0 / avg_time_per_frame if avg_time_per_frame > 0 else 0
                
                print(f"✓ Screen capture performance: {successful_captures}/10 successful")
                print(f"  Average time per frame: {avg_time_per_frame*1000:.2f}ms")
                print(f"  Estimated FPS: {fps:.1f}")
                
                # Performance should be reasonable (at least 10 FPS)
                self.assertGreater(fps, 10, "Screen capture should achieve at least 10 FPS")
            else:
                print("! No successful captures (may be expected in test environment)")
            
        except Exception as e:
            self.fail(f"Screen capture performance test failed: {e}")
    
    def test_memory_usage_monitoring(self):
        """Test memory usage monitoring on Windows 11"""
        try:
            import psutil
            
            # Get initial memory usage
            process = psutil.Process()
            initial_memory = process.memory_info().rss / 1024 / 1024  # MB
            
            # Create some components to test memory usage
            screen_capture = ScreenCapture()
            monitors = screen_capture.get_monitors()
            
            # Get memory usage after component creation
            final_memory = process.memory_info().rss / 1024 / 1024  # MB
            memory_increase = final_memory - initial_memory
            
            print(f"✓ Memory usage monitoring:")
            print(f"  Initial memory: {initial_memory:.1f} MB")
            print(f"  Final memory: {final_memory:.1f} MB")
            print(f"  Memory increase: {memory_increase:.1f} MB")
            
            # Memory increase should be reasonable (less than 100MB for basic components)
            self.assertLess(memory_increase, 100, "Memory increase should be reasonable")
            
        except ImportError:
            self.skipTest("psutil not available")
        except Exception as e:
            self.fail(f"Memory usage monitoring test failed: {e}")

def run_windows11_compatibility_tests():
    """Run all Windows 11 compatibility tests"""
    print("=" * 60)
    print("WINDOWS 11 COMPATIBILITY TESTS")
    print("=" * 60)
    
    if platform.system() != "Windows":
        print("! Skipping Windows 11 tests - not running on Windows")
        return True
    
    # Create test suite
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Add test classes
    suite.addTests(loader.loadTestsFromTestCase(TestWindows11Features))
    suite.addTests(loader.loadTestsFromTestCase(TestWindows11SystemIntegration))
    suite.addTests(loader.loadTestsFromTestCase(TestWindows11Performance))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    print("\n" + "=" * 60)
    if result.wasSuccessful():
        print("✓ ALL WINDOWS 11 COMPATIBILITY TESTS PASSED!")
    else:
        print("✗ Some Windows 11 compatibility tests failed")
        for failure in result.failures:
            print(f"FAILURE: {failure[0]}")
        for error in result.errors:
            print(f"ERROR: {error[0]}")
    
    print("=" * 60)
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_windows11_compatibility_tests()
    sys.exit(0 if success else 1)
