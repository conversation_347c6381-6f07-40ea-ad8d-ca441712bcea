{"enabled": true, "body_parts": {"breasts": {"enabled": true, "method": "blur_heavy", "intensity": 0.7, "color": "#000000", "custom_image_path": "", "blur_radius": 20, "pixel_size": 12, "opacity": 0.8, "shape": "ellipse", "border_width": 0, "border_color": "#FFFFFF", "animated": false, "animation_speed": 1.0, "padding": 8, "min_size": 20, "max_size": 500}, "genitals": {"enabled": true, "method": "pixelate_light", "intensity": 0.9, "color": "#000000", "custom_image_path": "", "blur_radius": 10, "pixel_size": 12, "opacity": 0.8, "shape": "rectangle", "border_width": 0, "border_color": "#FFFFFF", "animated": false, "animation_speed": 1.0, "padding": 8, "min_size": 20, "max_size": 500}, "buttocks": {"enabled": true, "method": "pixelate_light", "intensity": 0.7, "color": "#000000", "custom_image_path": "", "blur_radius": 10, "pixel_size": 8, "opacity": 0.8, "shape": "rectangle", "border_width": 0, "border_color": "#FFFFFF", "animated": false, "animation_speed": 1.0, "padding": 8, "min_size": 20, "max_size": 500}}, "detection": {"confidence_threshold": 0.7, "frame_skip": 2, "roi_enabled": true, "gpu_acceleration": true, "input_size": [320, 320], "max_detections": 50, "nms_threshold": 0.4, "roi_regions": null, "roi_auto_detect": true, "roi_update_interval": 30, "cache_enabled": true, "cache_size": 100, "cache_similarity_threshold": 0.95}, "performance": {"max_fps": 30, "max_resolution": [1920, 1080], "thread_count": 4, "memory_limit_mb": 1024, "adaptive_quality": true, "target_cpu_usage": 70.0, "target_memory_usage": 80.0, "frame_buffer_size": 10, "processing_timeout": 0.1, "quality_levels": {"1": {"input_size": [224, 224], "frame_skip": 5, "confidence": 0.8}, "2": {"input_size": [256, 256], "frame_skip": 4, "confidence": 0.75}, "3": {"input_size": [320, 320], "frame_skip": 3, "confidence": 0.7}, "4": {"input_size": [384, 384], "frame_skip": 2, "confidence": 0.65}, "5": {"input_size": [416, 416], "frame_skip": 1, "confidence": 0.6}}, "min_quality_level": 1, "max_quality_level": 5, "stats_update_interval": 1.0, "performance_history_size": 100, "gc_interval": 60, "memory_cleanup_threshold": 0.9}, "hotkeys": {"toggle_censoring": "ctrl+shift+c", "toggle_breasts": "ctrl+shift+b", "toggle_genitals": "ctrl+shift+g", "toggle_buttocks": "ctrl+shift+u", "emergency_disable": "ctrl+shift+x", "quick_preset_minimal": "ctrl+shift+1", "quick_preset_moderate": "ctrl+shift+2", "quick_preset_maximum": "ctrl+shift+3", "temporary_disable": "ctrl+shift+t", "stealth_mode": "ctrl+shift+s", "increase_intensity": "ctrl+shift+plus", "decrease_intensity": "ctrl+shift+minus", "enabled": true, "cooldown_ms": 500, "stealth_mode_active": false, "temporary_disable_duration": 30}}