{"enabled": false, "body_parts": {"breasts": {"enabled": true, "method": "blur_heavy", "intensity": 0.7, "color": "#000000", "custom_image_path": "", "blur_radius": 20, "pixel_size": 12, "opacity": 0.8, "shape": "ellipse", "border_width": 0, "border_color": "#FFFFFF", "animated": false, "animation_speed": 1.0, "padding": 8, "min_size": 20, "max_size": 500}, "genitals": {"enabled": true, "method": "pixelate_light", "intensity": 0.9, "color": "#000000", "custom_image_path": "", "blur_radius": 10, "pixel_size": 12, "opacity": 0.8, "shape": "rectangle", "border_width": 0, "border_color": "#FFFFFF", "animated": false, "animation_speed": 1.0, "padding": 8, "min_size": 20, "max_size": 500}, "buttocks": {"enabled": true, "method": "pixelate_light", "intensity": 0.7, "color": "#000000", "custom_image_path": "", "blur_radius": 10, "pixel_size": 8, "opacity": 0.8, "shape": "rectangle", "border_width": 0, "border_color": "#FFFFFF", "animated": false, "animation_speed": 1.0, "padding": 8, "min_size": 20, "max_size": 500}}, "detection": {"confidence_threshold": 0.7, "frame_skip": 2, "roi_enabled": true, "gpu_acceleration": true}, "performance": {"max_fps": 30, "max_resolution": [1920, 1080], "thread_count": 4, "memory_limit_mb": 1024}, "hotkeys": {"toggle_censoring": "ctrl+shift+c", "toggle_breasts": "ctrl+shift+b", "toggle_genitals": "ctrl+shift+g", "toggle_buttocks": "ctrl+shift+u", "emergency_disable": "ctrl+shift+x"}}