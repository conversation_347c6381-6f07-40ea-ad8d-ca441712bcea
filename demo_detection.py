#!/usr/bin/env python3
"""
Demo script for nudity detection functionality
Run this after installing dependencies to test detection engine
"""

import sys
import os
import time
import numpy as np

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_detection_engine():
    """Test detection engine functionality"""
    try:
        from detection.nudity_detector import NudityDetector, Detection, DetectionResult
        from config.settings import Body<PERSON>art
        
        print("Nudity Detection Engine Demo")
        print("=" * 35)
        
        # Create detection engine
        print("Initializing detection engine...")
        detector = NudityDetector()
        
        # Show initial configuration
        print(f"✓ Detector initialized")
        print(f"  Confidence threshold: {detector.confidence_threshold}")
        print(f"  Input size: {detector.input_size}")
        print(f"  Max detections: {detector.max_detections}")
        
        # Test configuration changes
        print("\nTesting configuration...")
        detector.set_confidence_threshold(0.8)
        detector.set_input_size(416, 416)
        print(f"✓ Configuration updated")
        print(f"  New confidence threshold: {detector.confidence_threshold}")
        print(f"  New input size: {detector.input_size}")
        
        # Test class mapping
        print("\nTesting class mapping...")
        test_classes = [
            'FEMALE_BREAST_EXPOSED',
            'MALE_GENITALIA_EXPOSED', 
            'BUTTOCKS_EXPOSED',
            'UNKNOWN_CLASS'
        ]
        
        for class_name in test_classes:
            mapped = detector.CLASS_MAPPING.get(class_name)
            if mapped:
                print(f"  {class_name} -> {mapped.value}")
            else:
                print(f"  {class_name} -> Not mapped")
        
        # Test frame preprocessing
        print("\nTesting frame preprocessing...")
        test_frames = [
            np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8),
            np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8),
            np.random.randint(0, 255, (1080, 1920, 3), dtype=np.uint8),
        ]
        
        for i, frame in enumerate(test_frames):
            processed = detector.preprocess_frame(frame)
            print(f"  Frame {i+1}: {frame.shape} -> {processed.shape}")
        
        # Test detection filtering
        print("\nTesting detection filtering...")
        test_detections = [
            Detection(BodyPart.BREASTS, 0.9, (0.1, 0.1, 0.2, 0.2), "BREAST"),
            Detection(BodyPart.GENITALS, 0.8, (0.3, 0.3, 0.4, 0.4), "GENITALS"),
            Detection(BodyPart.BUTTOCKS, 0.7, (0.5, 0.5, 0.6, 0.6), "BUTTOCKS"),
        ]
        
        # Test different filter combinations
        filter_tests = [
            ([BodyPart.BREASTS], 1),
            ([BodyPart.BREASTS, BodyPart.GENITALS], 2),
            ([], 0),
            ([BodyPart.BREASTS, BodyPart.GENITALS, BodyPart.BUTTOCKS], 3)
        ]
        
        for enabled_parts, expected_count in filter_tests:
            filtered = detector.filter_detections_by_body_parts(test_detections, enabled_parts)
            print(f"  Filter {[p.value for p in enabled_parts]}: {len(filtered)}/{expected_count} detections")
            assert len(filtered) == expected_count, f"Expected {expected_count}, got {len(filtered)}"
        
        # Test performance stats
        print("\nTesting performance statistics...")
        stats = detector.get_performance_stats()
        print(f"  Model loaded: {stats['model_loaded']}")
        print(f"  Total frames: {stats['total_frames']}")
        print(f"  Detection available: {stats['detection_available']}")
        print(f"  Confidence threshold: {stats['confidence_threshold']}")
        
        print("\n✓ Detection engine basic tests completed successfully!")
        return True
        
    except ImportError as e:
        print(f"✗ Missing dependencies: {e}")
        print("Please run 'python setup.py' to install required packages")
        return False
    except Exception as e:
        print(f"✗ Error during detection engine demo: {e}")
        return False

def test_model_loading():
    """Test actual model loading (requires internet)"""
    try:
        from detection.nudity_detector import NudityDetector
        import nudenet
        
        print("\nModel Loading Test")
        print("=" * 20)
        print("⚠️  This test requires internet connection and may take several minutes")
        print("   The NudeNet model (~100MB) will be downloaded on first run")
        
        response = input("\nProceed with model loading test? (y/N): ")
        if response.lower() != 'y':
            print("Skipping model loading test")
            return True
        
        detector = NudityDetector()
        
        print("\nLoading NudeNet model...")
        start_time = time.time()
        
        success = detector.load_model()
        load_time = time.time() - start_time
        
        if success:
            print(f"✓ Model loaded successfully in {load_time:.2f} seconds")
            
            # Test actual detection with random frame
            print("\nTesting detection with random frame...")
            test_frame = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
            
            detection_start = time.time()
            result = detector.detect_nudity(test_frame)
            detection_time = time.time() - detection_start
            
            print(f"✓ Detection completed in {detection_time:.3f} seconds")
            print(f"  Detections found: {len(result.detections)}")
            print(f"  Frame shape: {result.frame_shape}")
            print(f"  Processing time: {result.processing_time:.3f}s")
            
            # Show performance stats
            stats = detector.get_performance_stats()
            print(f"\nPerformance Statistics:")
            print(f"  Total frames processed: {stats['total_frames']}")
            print(f"  Average detection time: {stats['avg_detection_time_ms']:.1f}ms")
            
            return True
        else:
            print("✗ Model loading failed")
            print("  This could be due to network issues or missing dependencies")
            return False
            
    except ImportError as e:
        print(f"✗ NudeNet not available: {e}")
        print("Please ensure all dependencies are installed")
        return False
    except Exception as e:
        print(f"✗ Model loading test failed: {e}")
        return False

def test_integration():
    """Test integration with app controller"""
    try:
        from app_controller import AppController
        from config.settings import Settings
        
        print("\nIntegration Test")
        print("=" * 16)
        
        # Create settings
        settings = Settings("test_integration_config.json")
        
        # Create app controller (without GUI)
        print("Creating app controller...")
        controller = AppController(settings, headless=True)
        
        # Check components
        print(f"✓ App controller created")
        print(f"  Screen capture: {controller.screen_capture is not None}")
        print(f"  Detection engine: {controller.detection_engine is not None}")
        
        if controller.detection_engine:
            print(f"  Detection confidence: {controller.detection_engine.confidence_threshold}")
            
            # Test detection configuration
            controller.set_detection_confidence(0.9)
            print(f"  Updated confidence: {controller.detection_engine.confidence_threshold}")
        
        # Get status
        status = controller.get_status()
        print(f"\nStatus:")
        print(f"  Running: {status['running']}")
        print(f"  Enabled: {status['enabled']}")
        print(f"  Components: {status['components']}")
        
        # Cleanup
        if os.path.exists("test_integration_config.json"):
            os.remove("test_integration_config.json")
        
        print("✓ Integration test completed successfully!")
        return True
        
    except Exception as e:
        print(f"✗ Integration test failed: {e}")
        return False

def main():
    """Run demo"""
    print("Censor App - Detection Engine Demo")
    print("=" * 40)
    
    # Check if basic dependencies are available
    try:
        import numpy
        print("✓ NumPy available")
    except ImportError:
        print("✗ NumPy not available")
        print("Please run: python setup.py")
        return False
    
    # Run tests
    success = True
    
    if not test_detection_engine():
        success = False
    
    if not test_model_loading():
        success = False
    
    if not test_integration():
        success = False
    
    if success:
        print("\n🎉 All detection engine tests passed!")
        print("\nThe detection system is working correctly.")
        print("Next steps:")
        print("1. Run the full application: python main.py")
        print("2. Or test screen capture + detection: python demo_screen_capture.py")
    else:
        print("\n❌ Some tests failed. Please check the errors above.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
