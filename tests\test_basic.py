"""
Basic tests for Censor App
"""

import unittest
import sys
import os

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from config.settings import Settings, BodyPart, CensorMethod

class TestSettings(unittest.TestCase):
    """Test settings functionality"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.settings = Settings("test_config.json")
    
    def tearDown(self):
        """Clean up test fixtures"""
        if os.path.exists("test_config.json"):
            os.remove("test_config.json")
    
    def test_default_settings(self):
        """Test default settings values"""
        self.assertFalse(self.settings.enabled)
        self.assertEqual(self.settings.detection.confidence_threshold, 0.7)
        self.assertEqual(self.settings.performance.max_fps, 30)
    
    def test_body_part_settings(self):
        """Test body part specific settings"""
        breasts_setting = self.settings.get_body_part_setting(BodyPart.BREASTS)
        self.assertTrue(breasts_setting.enabled)
        self.assertEqual(breasts_setting.method, CensorMethod.BLUR)
        self.assertEqual(breasts_setting.intensity, 0.8)
    
    def test_toggle_body_part(self):
        """Test toggling body part censoring"""
        original_state = self.settings.get_body_part_setting(BodyPart.BREASTS).enabled
        new_state = self.settings.toggle_body_part(BodyPart.BREASTS)
        self.assertEqual(new_state, not original_state)
    
    def test_toggle_censoring(self):
        """Test toggling overall censoring"""
        original_state = self.settings.enabled
        new_state = self.settings.toggle_censoring()
        self.assertEqual(new_state, not original_state)
    
    def test_save_load_settings(self):
        """Test saving and loading settings"""
        # Modify settings
        self.settings.enabled = True
        self.settings.detection.confidence_threshold = 0.9
        self.settings.save()
        
        # Create new settings instance and load
        new_settings = Settings("test_config.json")
        self.assertTrue(new_settings.enabled)
        self.assertEqual(new_settings.detection.confidence_threshold, 0.9)

class TestImports(unittest.TestCase):
    """Test that all modules can be imported"""
    
    def test_import_settings(self):
        """Test importing settings module"""
        from config.settings import Settings, BodyPart, CensorMethod
        self.assertTrue(True)  # If we get here, import succeeded
    
    def test_import_app_controller(self):
        """Test importing app controller"""
        # This test might fail until PyQt6 is installed
        try:
            from app_controller import AppController
            self.assertTrue(True)
        except ImportError as e:
            self.skipTest(f"PyQt6 not available: {e}")

if __name__ == '__main__':
    unittest.main()
