#!/usr/bin/env python3
"""
Comprehensive test script for GUI functionality
"""

import sys
import os
import time
import logging

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTimer
from PyQt6.QtTest import QTest

from src.config.settings import Settings
from src.app_controller import App<PERSON><PERSON>roller

def test_gui_functionality():
    """Test comprehensive GUI functionality"""
    print("Comprehensive GUI Functionality Test")
    print("=" * 50)
    
    # Create QApplication
    app = QApplication(sys.argv)
    
    try:
        # Initialize controller with GUI
        settings = Settings()
        controller = AppController(settings, headless=False)
        
        print("✓ GUI components initialized")
        
        # Test 1: System Tray Functionality
        print("\n1. Testing System Tray...")
        system_tray = controller.system_tray
        
        if system_tray:
            # Test show/hide
            system_tray.show()
            assert system_tray.tray_icon.isVisible(), "System tray should be visible"
            print("✓ System tray visibility works")
            
            # Test status updates
            system_tray.update_status("Testing")
            assert "Testing" in system_tray.tray_icon.toolTip(), "Tooltip should update"
            print("✓ System tray status updates work")
            
            # Test message display
            system_tray.show_message("Test", "Test message")
            print("✓ System tray notifications work")
            
            # Test status method
            status = system_tray.get_status()
            assert isinstance(status, dict), "Status should be a dictionary"
            assert 'visible' in status, "Status should include visibility"
            print("✓ System tray status reporting works")
        
        # Test 2: Main Window Functionality
        print("\n2. Testing Main Window...")
        main_window = controller.main_window
        
        if main_window:
            # Test window display
            main_window.show()
            assert main_window.isVisible(), "Main window should be visible"
            print("✓ Main window display works")
            
            # Test tab structure
            tab_count = main_window.tab_widget.count()
            assert tab_count >= 6, f"Should have at least 6 tabs, got {tab_count}"
            print(f"✓ Main window has {tab_count} tabs")
            
            # Test status tab components
            main_window.tab_widget.setCurrentIndex(0)  # Status tab should be first
            print("✓ Status tab accessible")
            
            # Test status refresh
            main_window._refresh_status()
            print("✓ Status refresh functionality works")
            
            # Test settings loading
            main_window._load_settings()
            print("✓ Settings loading works")
        
        # Test 3: Signal Connections
        print("\n3. Testing Signal Connections...")
        
        # Test state change signals
        original_state = controller.get_state()
        controller.state_changed.emit("testing")
        
        # Give time for signal processing
        QTest.qWait(100)
        print("✓ State change signals work")
        
        # Test status change signals
        controller.status_changed.emit("Test status")
        QTest.qWait(100)
        print("✓ Status change signals work")
        
        # Test error signals
        controller.error_occurred.emit("Test error")
        QTest.qWait(100)
        print("✓ Error signals work")
        
        # Test 4: Settings Integration
        print("\n4. Testing Settings Integration...")

        if main_window:
            # Test settings UI update
            original_enabled = settings.enabled

            # Change the UI control directly
            main_window.enabled_checkbox.setChecked(not original_enabled)

            # Apply settings from UI
            main_window._apply_settings()

            # Verify settings were applied
            assert settings.enabled != original_enabled, "Settings should have changed"
            print("✓ Settings application works")

            # Reset settings
            main_window.enabled_checkbox.setChecked(original_enabled)
            main_window._apply_settings()
            main_window._load_settings()
            print("✓ Settings reset works")
        
        # Test 5: Performance Monitoring
        print("\n5. Testing Performance Monitoring...")
        
        if main_window:
            # Check performance labels exist
            assert hasattr(main_window, 'capture_fps_label'), "Should have capture FPS label"
            assert hasattr(main_window, 'processing_fps_label'), "Should have processing FPS label"
            assert hasattr(main_window, 'queue_size_label'), "Should have queue size label"
            assert hasattr(main_window, 'dropped_frames_label'), "Should have dropped frames label"
            print("✓ Performance monitoring labels exist")
            
            # Test refresh updates labels
            main_window._refresh_status()
            print("✓ Performance monitoring updates work")
        
        # Test 6: Component Status Display
        print("\n6. Testing Component Status Display...")
        
        if main_window:
            # Check component labels exist
            assert hasattr(main_window, 'component_labels'), "Should have component labels"
            assert len(main_window.component_labels) > 0, "Should have component status labels"
            print(f"✓ Component status display has {len(main_window.component_labels)} components")
        
        # Test 7: Log Display
        print("\n7. Testing Log Display...")
        
        if main_window:
            # Test log entry addition
            main_window._add_log_entry("Test log entry")
            log_text = main_window.log_display.toPlainText()
            assert "Test log entry" in log_text, "Log entry should appear in display"
            print("✓ Log display functionality works")
        
        # Test 8: Integration with AppController
        print("\n8. Testing AppController Integration...")
        
        # Start the application
        controller.start()
        print("✓ Application startup integration works")
        
        # Test status reporting
        status = controller.get_status()
        assert isinstance(status, dict), "Status should be a dictionary"
        print("✓ Status reporting integration works")
        
        print("\n" + "=" * 50)
        print("✅ All GUI functionality tests passed!")
        
        print("\nGUI Features Successfully Tested:")
        print("- ✓ System tray integration and notifications")
        print("- ✓ Main window with tabbed interface")
        print("- ✓ Real-time status monitoring")
        print("- ✓ Performance metrics display")
        print("- ✓ Component status indicators")
        print("- ✓ Settings management interface")
        print("- ✓ Signal/slot communication")
        print("- ✓ Log display and activity tracking")
        print("- ✓ AppController integration")
        print("- ✓ Error handling and notifications")
        
        return True
        
    except Exception as e:
        print(f"\n❌ GUI functionality test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # Cleanup
        try:
            if 'controller' in locals():
                controller.stop_censoring()
                if controller.main_window:
                    controller.main_window.close()
                if controller.system_tray:
                    controller.system_tray.hide()
        except:
            pass
        
        app.quit()

if __name__ == "__main__":
    # Set up logging
    logging.basicConfig(level=logging.INFO)
    
    success = test_gui_functionality()
    sys.exit(0 if success else 1)
