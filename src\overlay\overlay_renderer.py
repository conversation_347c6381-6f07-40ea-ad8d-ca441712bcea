"""
Overlay Rendering System for Censor App
Creates transparent overlay windows for censoring detected content
"""

import logging
import sys
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

try:
    from PyQt6.QtWidgets import QApplication, QWidget, QLabel
    from PyQt6.QtCore import Qt, QTimer, QRect, pyqtSignal
    from PyQt6.QtGui import QPixmap, QPainter, QColor, QBrush, QPen
    import numpy as np
    OVERLAY_AVAILABLE = True
except ImportError:
    OVERLAY_AVAILABLE = False

from src.config.settings import CensorMethod, BodyPart
from src.detection.nudity_detector import Detection
from src.utils.image_utils import ImageProcessor

@dataclass
class OverlayRegion:
    """Represents a region to be censored with overlay"""
    x: int
    y: int
    width: int
    height: int
    body_part: BodyPart
    censor_method: CensorMethod
    confidence: float

    # Advanced settings
    intensity: float = 0.8
    opacity: float = 0.8
    color: str = "#000000"
    custom_image_path: str = ""
    blur_radius: int = 15
    pixel_size: int = 10
    shape: str = "rectangle"
    border_width: int = 0
    border_color: str = "#FFFFFF"

class OverlayWindow(QWidget):
    """Transparent overlay window for censoring"""
    
    def __init__(self, monitor_geometry: QRect):
        super().__init__()
        self.logger = logging.getLogger(__name__)
        
        # Store monitor geometry
        self.monitor_geometry = monitor_geometry
        
        # Overlay regions to render
        self.regions: List[OverlayRegion] = []
        
        # Setup window properties
        self._setup_window()
        
        # Image processor for creating censored content
        self.image_processor = ImageProcessor()
        
        self.logger.debug(f"Overlay window created for monitor: {monitor_geometry}")
    
    def _setup_window(self):
        """Setup window properties for transparent overlay"""
        # Set window flags for transparent overlay that stays on top
        self.setWindowFlags(
            Qt.WindowType.FramelessWindowHint |
            Qt.WindowType.WindowStaysOnTopHint |
            Qt.WindowType.Tool |
            Qt.WindowType.WindowTransparentForInput
        )
        
        # Set window attributes
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.setAttribute(Qt.WidgetAttribute.WA_NoSystemBackground)
        self.setAttribute(Qt.WidgetAttribute.WA_TransparentForMouseEvents)
        
        # Set geometry to cover the entire monitor
        self.setGeometry(self.monitor_geometry)
        
        # Make sure window is always on top
        self.raise_()
        self.activateWindow()
    
    def update_regions(self, regions: List[OverlayRegion]):
        """Update the regions to be censored"""
        self.regions = regions
        self.update()  # Trigger repaint
    
    def paintEvent(self, event):
        """Paint the overlay regions"""
        if not self.regions:
            return
        
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        try:
            for region in self.regions:
                self._paint_region(painter, region)
        except Exception as e:
            self.logger.error(f"Error painting overlay region: {e}")
        finally:
            painter.end()
    
    def _paint_region(self, painter: QPainter, region: OverlayRegion):
        """Paint a single censored region"""
        # Create rectangle for the region
        rect = QRect(region.x, region.y, region.width, region.height)

        # Apply censoring based on method
        if region.censor_method in [CensorMethod.BLUR, CensorMethod.BLUR_LIGHT, CensorMethod.BLUR_HEAVY]:
            self._paint_blur_region(painter, rect, region)
        elif region.censor_method in [CensorMethod.PIXELATE, CensorMethod.PIXELATE_LIGHT, CensorMethod.PIXELATE_HEAVY]:
            self._paint_pixelate_region(painter, rect, region)
        elif region.censor_method == CensorMethod.BLACK_BAR:
            self._paint_solid_region(painter, rect, QColor(0, 0, 0, 200))
        elif region.censor_method == CensorMethod.WHITE_BAR:
            self._paint_solid_region(painter, rect, QColor(255, 255, 255, 200))
        elif region.censor_method == CensorMethod.SOLID_COLOR:
            self._paint_solid_region(painter, rect, QColor(128, 128, 128, 200))
        elif region.censor_method == CensorMethod.MOSAIC:
            self._paint_mosaic_region(painter, rect, region)
        elif region.censor_method == CensorMethod.NOISE:
            self._paint_noise_region(painter, rect, region)
        elif region.censor_method == CensorMethod.SWIRL:
            self._paint_swirl_region(painter, rect, region)
        elif region.censor_method == CensorMethod.CUSTOM_IMAGE:
            self._paint_custom_image_region(painter, rect, region)
        else:
            # Default to black bar
            self._paint_solid_region(painter, rect, QColor(0, 0, 0, 200))
    
    def _paint_blur_region(self, painter: QPainter, rect: QRect, region: OverlayRegion):
        """Paint a blurred region effect"""
        # Determine blur intensity based on method
        if region.censor_method == CensorMethod.BLUR_LIGHT:
            alpha = 120
            dot_spacing = 6
        elif region.censor_method == CensorMethod.BLUR_HEAVY:
            alpha = 200
            dot_spacing = 2
        else:  # Regular blur
            alpha = 150
            dot_spacing = 4

        # Create a semi-transparent overlay
        color = QColor(100, 100, 100, alpha)
        painter.fillRect(rect, color)

        # Add visual texture to simulate blur
        painter.setPen(QPen(QColor(120, 120, 120, alpha // 2), 1))
        for i in range(0, rect.width(), dot_spacing):
            for j in range(0, rect.height(), dot_spacing):
                painter.drawPoint(rect.x() + i, rect.y() + j)
    
    def _paint_pixelate_region(self, painter: QPainter, rect: QRect, region: OverlayRegion):
        """Paint a pixelated region effect"""
        # Determine pixel size based on method
        if region.censor_method == CensorMethod.PIXELATE_LIGHT:
            pixel_size = max(12, min(rect.width(), rect.height()) // 8)
            alpha = 160
        elif region.censor_method == CensorMethod.PIXELATE_HEAVY:
            pixel_size = max(4, min(rect.width(), rect.height()) // 15)
            alpha = 200
        else:  # Regular pixelate
            pixel_size = max(8, min(rect.width(), rect.height()) // 10)
            alpha = 180

        # Fill with base color
        base_color = QColor(80, 80, 80, alpha)
        painter.fillRect(rect, base_color)

        # Add pixelation pattern
        colors = [
            QColor(60, 60, 60, alpha),
            QColor(100, 100, 100, alpha),
            QColor(120, 120, 120, alpha)
        ]

        for x in range(rect.x(), rect.x() + rect.width(), pixel_size):
            for y in range(rect.y(), rect.y() + rect.height(), pixel_size):
                # Use different colors for pixelation effect
                color = colors[(x + y) % len(colors)]
                pixel_rect = QRect(x, y, pixel_size, pixel_size)
                painter.fillRect(pixel_rect, color)
    
    def _paint_solid_region(self, painter: QPainter, rect: QRect, color: QColor):
        """Paint a solid colored region"""
        painter.fillRect(rect, color)

        # Add subtle border
        border_color = QColor(color.red(), color.green(), color.blue(), min(255, color.alpha() + 50))
        painter.setPen(QPen(border_color, 1))
        painter.drawRect(rect)

    def _paint_mosaic_region(self, painter: QPainter, rect: QRect, region: OverlayRegion):
        """Paint a mosaic effect region"""
        tile_size = max(6, min(rect.width(), rect.height()) // 12)

        # Create mosaic pattern with varied colors
        base_colors = [
            QColor(80, 80, 80, 180),
            QColor(100, 100, 100, 180),
            QColor(120, 120, 120, 180),
            QColor(60, 60, 60, 180),
            QColor(140, 140, 140, 180)
        ]

        for x in range(rect.x(), rect.x() + rect.width(), tile_size):
            for y in range(rect.y(), rect.y() + rect.height(), tile_size):
                # Select color based on position
                color_index = ((x // tile_size) + (y // tile_size)) % len(base_colors)
                color = base_colors[color_index]

                tile_rect = QRect(x, y,
                                min(tile_size, rect.x() + rect.width() - x),
                                min(tile_size, rect.y() + rect.height() - y))
                painter.fillRect(tile_rect, color)

                # Add border for mosaic effect
                painter.setPen(QPen(QColor(40, 40, 40, 100), 1))
                painter.drawRect(tile_rect)

    def _paint_noise_region(self, painter: QPainter, rect: QRect, region: OverlayRegion):
        """Paint a noise effect region"""
        # Fill with base color
        painter.fillRect(rect, QColor(100, 100, 100, 160))

        # Add random noise pattern
        import random
        painter.setPen(QPen(QColor(0, 0, 0, 80), 1))

        # Generate random dots for noise effect
        num_dots = (rect.width() * rect.height()) // 20
        for _ in range(num_dots):
            x = rect.x() + random.randint(0, rect.width() - 1)
            y = rect.y() + random.randint(0, rect.height() - 1)

            # Random color variation
            gray = random.randint(50, 200)
            alpha = random.randint(60, 120)
            color = QColor(gray, gray, gray, alpha)
            painter.setPen(QPen(color, 1))
            painter.drawPoint(x, y)

    def _paint_swirl_region(self, painter: QPainter, rect: QRect, region: OverlayRegion):
        """Paint a swirl effect region"""
        # Fill with base color
        painter.fillRect(rect, QColor(90, 90, 90, 170))

        # Create swirl pattern with concentric circles
        center_x = rect.x() + rect.width() // 2
        center_y = rect.y() + rect.height() // 2
        max_radius = min(rect.width(), rect.height()) // 2

        painter.setPen(QPen(QColor(120, 120, 120, 100), 2))

        # Draw concentric circles for swirl effect
        for radius in range(5, max_radius, 8):
            painter.drawEllipse(center_x - radius, center_y - radius,
                              radius * 2, radius * 2)

        # Add spiral lines
        painter.setPen(QPen(QColor(140, 140, 140, 80), 1))
        import math
        for angle in range(0, 360, 30):
            rad = math.radians(angle)
            x1 = center_x + int(max_radius * 0.3 * math.cos(rad))
            y1 = center_y + int(max_radius * 0.3 * math.sin(rad))
            x2 = center_x + int(max_radius * 0.8 * math.cos(rad))
            y2 = center_y + int(max_radius * 0.8 * math.sin(rad))
            painter.drawLine(x1, y1, x2, y2)

    def _paint_custom_image_region(self, painter: QPainter, rect: QRect, region: OverlayRegion):
        """Paint a custom image overlay region"""
        # For now, use a placeholder pattern
        # In a full implementation, this would load and scale the custom image

        # Fill with semi-transparent background
        painter.fillRect(rect, QColor(128, 128, 128, 150))

        # Add placeholder pattern
        painter.setPen(QPen(QColor(200, 200, 200, 180), 2))

        # Draw diagonal lines as placeholder
        for i in range(0, rect.width() + rect.height(), 10):
            x1 = rect.x() + max(0, i - rect.height())
            y1 = rect.y() + max(0, rect.height() - i)
            x2 = rect.x() + min(rect.width(), i)
            y2 = rect.y() + max(0, rect.height() - i + rect.width())

            if x1 < rect.x() + rect.width() and y1 < rect.y() + rect.height():
                painter.drawLine(x1, y1, x2, y2)
    
    def show_overlay(self):
        """Show the overlay window"""
        self.show()
        self.raise_()
        self.activateWindow()
    
    def hide_overlay(self):
        """Hide the overlay window"""
        self.hide()

class OverlayRenderer:
    """Main overlay rendering system"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Check if overlay is available
        if not OVERLAY_AVAILABLE:
            self.logger.warning("Overlay libraries not available. Install dependencies first.")
            return
        
        # Overlay windows for each monitor
        self.overlay_windows: Dict[int, OverlayWindow] = {}
        
        # Current application instance
        self.app = QApplication.instance()
        if self.app is None:
            self.logger.warning("No QApplication instance found. Overlay may not work properly.")
        
        # Performance settings
        self.min_region_size = 20  # Minimum size for overlay regions
        self.max_regions_per_monitor = 50  # Maximum regions per monitor
        
        # State
        self.enabled = True
        self.visible = False
        
        self.logger.info("Overlay renderer initialized")
    
    def setup_monitors(self, monitor_geometries: List[QRect]):
        """Setup overlay windows for monitors"""
        if not OVERLAY_AVAILABLE:
            return False
        
        try:
            # Clear existing windows
            self.clear_overlays()
            
            # Create overlay window for each monitor
            for i, geometry in enumerate(monitor_geometries):
                overlay_window = OverlayWindow(geometry)
                self.overlay_windows[i] = overlay_window
                self.logger.debug(f"Created overlay window for monitor {i}: {geometry}")
            
            self.logger.info(f"Setup overlay windows for {len(monitor_geometries)} monitors")
            return True
            
        except Exception as e:
            self.logger.error(f"Error setting up monitor overlays: {e}")
            return False
    
    def update_overlays(self, detections: List[Detection], monitor_index: int = 0, 
                       frame_shape: Tuple[int, int] = (1080, 1920),
                       censor_settings: Optional[Dict] = None):
        """Update overlay regions based on detections"""
        if not OVERLAY_AVAILABLE or not self.enabled:
            return
        
        if monitor_index not in self.overlay_windows:
            self.logger.warning(f"No overlay window for monitor {monitor_index}")
            return
        
        try:
            # Convert detections to overlay regions
            regions = self._detections_to_regions(
                detections, frame_shape, censor_settings or {}
            )
            
            # Filter regions by size
            regions = [r for r in regions if r.width >= self.min_region_size and r.height >= self.min_region_size]
            
            # Limit number of regions
            if len(regions) > self.max_regions_per_monitor:
                # Sort by confidence and take top regions
                regions.sort(key=lambda r: r.confidence, reverse=True)
                regions = regions[:self.max_regions_per_monitor]
            
            # Update overlay window
            overlay_window = self.overlay_windows[monitor_index]
            overlay_window.update_regions(regions)
            
            self.logger.debug(f"Updated {len(regions)} overlay regions for monitor {monitor_index}")
            
        except Exception as e:
            self.logger.error(f"Error updating overlays: {e}")
    
    def _detections_to_regions(self, detections: List[Detection],
                              frame_shape: Tuple[int, int],
                              censor_settings: Dict) -> List[OverlayRegion]:
        """Convert detections to overlay regions with enhanced settings"""
        regions = []
        frame_height, frame_width = frame_shape

        for detection in detections:
            # Convert normalized coordinates to pixel coordinates
            x1, y1, x2, y2 = detection.bbox
            x = int(x1 * frame_width)
            y = int(y1 * frame_height)
            width = int((x2 - x1) * frame_width)
            height = int((y2 - y1) * frame_height)

            # Get censor settings for this body part
            body_part_settings = censor_settings.get(detection.body_part)
            if body_part_settings:
                if hasattr(body_part_settings, 'method'):
                    # It's a CensorSettings object
                    censor_method = body_part_settings.method
                    intensity = body_part_settings.intensity
                    opacity = body_part_settings.opacity
                    color = body_part_settings.color
                    custom_image_path = body_part_settings.custom_image_path
                    blur_radius = body_part_settings.blur_radius
                    pixel_size = body_part_settings.pixel_size
                    shape = body_part_settings.shape
                    border_width = body_part_settings.border_width
                    border_color = body_part_settings.border_color

                    # Apply padding if specified
                    padding = getattr(body_part_settings, 'padding', 5)
                    x = max(0, x - padding)
                    y = max(0, y - padding)
                    width = min(frame_width - x, width + 2 * padding)
                    height = min(frame_height - y, height + 2 * padding)
                else:
                    # It's just a CensorMethod
                    censor_method = body_part_settings
                    intensity = 0.8
                    opacity = 0.8
                    color = "#000000"
                    custom_image_path = ""
                    blur_radius = 15
                    pixel_size = 10
                    shape = "rectangle"
                    border_width = 0
                    border_color = "#FFFFFF"
            else:
                # Default settings
                censor_method = CensorMethod.BLACK_BAR
                intensity = 0.8
                opacity = 0.8
                color = "#000000"
                custom_image_path = ""
                blur_radius = 15
                pixel_size = 10
                shape = "rectangle"
                border_width = 0
                border_color = "#FFFFFF"

            # Create enhanced overlay region
            region = OverlayRegion(
                x=x, y=y, width=width, height=height,
                body_part=detection.body_part,
                censor_method=censor_method,
                confidence=detection.confidence,
                intensity=intensity,
                opacity=opacity,
                color=color,
                custom_image_path=custom_image_path,
                blur_radius=blur_radius,
                pixel_size=pixel_size,
                shape=shape,
                border_width=border_width,
                border_color=border_color
            )

            regions.append(region)

        return regions
    
    def show_overlays(self):
        """Show all overlay windows"""
        if not OVERLAY_AVAILABLE or not self.enabled:
            return
        
        for overlay_window in self.overlay_windows.values():
            overlay_window.show_overlay()
        
        self.visible = True
        self.logger.debug("Overlay windows shown")
    
    def hide_overlays(self):
        """Hide all overlay windows"""
        if not OVERLAY_AVAILABLE:
            return
        
        for overlay_window in self.overlay_windows.values():
            overlay_window.hide_overlay()
        
        self.visible = False
        self.logger.debug("Overlay windows hidden")
    
    def clear_overlays(self):
        """Clear all overlay regions"""
        for overlay_window in self.overlay_windows.values():
            overlay_window.update_regions([])
    
    def set_enabled(self, enabled: bool):
        """Enable or disable overlay rendering"""
        self.enabled = enabled
        if not enabled:
            self.hide_overlays()
        self.logger.info(f"Overlay rendering {'enabled' if enabled else 'disabled'}")
    
    def get_status(self) -> Dict:
        """Get overlay renderer status"""
        return {
            "available": OVERLAY_AVAILABLE,
            "enabled": self.enabled,
            "visible": self.visible,
            "monitor_count": len(self.overlay_windows),
            "min_region_size": self.min_region_size,
            "max_regions_per_monitor": self.max_regions_per_monitor
        }
    
    def __del__(self):
        """Cleanup when renderer is destroyed"""
        try:
            self.hide_overlays()
            for overlay_window in self.overlay_windows.values():
                overlay_window.close()
        except:
            pass
