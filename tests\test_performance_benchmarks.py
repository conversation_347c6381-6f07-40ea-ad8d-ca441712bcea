"""
Performance Benchmarking Tests for Censor App
Tests performance across different hardware configurations and scenarios
"""

import unittest
import sys
import os
import time
import threading
import statistics
import platform
from collections import defaultdict

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from src.capture.screen_capture import ScreenCapture
from src.detection.nudity_detector import NudityDetector
from src.overlay.overlay_renderer import OverlayRenderer
from src.performance.performance_manager import PerformanceManager

class TestSystemPerformance(unittest.TestCase):
    """Test system performance characteristics"""
    
    def setUp(self):
        """Set up test environment"""
        self.system_info = self._get_system_info()
        print(f"\nSystem Info: {self.system_info}")
    
    def _get_system_info(self):
        """Get system information for performance context"""
        info = {
            'platform': platform.system(),
            'processor': platform.processor(),
            'python_version': platform.python_version(),
        }
        
        try:
            import psutil
            info.update({
                'cpu_count': psutil.cpu_count(),
                'memory_gb': round(psutil.virtual_memory().total / (1024**3), 1),
                'cpu_freq_mhz': round(psutil.cpu_freq().current) if psutil.cpu_freq() else 'Unknown'
            })
        except ImportError:
            info.update({
                'cpu_count': 'Unknown',
                'memory_gb': 'Unknown',
                'cpu_freq_mhz': 'Unknown'
            })
        
        return info
    
    def test_screen_capture_performance_benchmark(self):
        """Benchmark screen capture performance"""
        capture = ScreenCapture()
        
        # Test different scenarios
        scenarios = [
            ('single_monitor', 0, 30),
            ('primary_monitor', None, 30),
        ]
        
        results = {}
        
        for scenario_name, monitor_index, num_captures in scenarios:
            print(f"\n--- {scenario_name.upper()} BENCHMARK ---")
            
            times = []
            successful_captures = 0
            
            # Warm up
            for _ in range(3):
                capture.capture_frame(monitor_index=monitor_index)
            
            # Benchmark
            start_time = time.time()
            for i in range(num_captures):
                frame_start = time.time()
                frame = capture.capture_frame(monitor_index=monitor_index)
                frame_end = time.time()
                
                if frame is not None:
                    times.append(frame_end - frame_start)
                    successful_captures += 1
            
            total_time = time.time() - start_time
            
            if times:
                avg_time = statistics.mean(times)
                min_time = min(times)
                max_time = max(times)
                std_dev = statistics.stdev(times) if len(times) > 1 else 0
                fps = 1.0 / avg_time if avg_time > 0 else 0
                
                results[scenario_name] = {
                    'successful_captures': successful_captures,
                    'total_captures': num_captures,
                    'avg_time_ms': avg_time * 1000,
                    'min_time_ms': min_time * 1000,
                    'max_time_ms': max_time * 1000,
                    'std_dev_ms': std_dev * 1000,
                    'fps': fps,
                    'total_time_s': total_time
                }
                
                print(f"✓ {scenario_name}: {successful_captures}/{num_captures} successful")
                print(f"  Average: {avg_time*1000:.1f}ms ({fps:.1f} FPS)")
                print(f"  Range: {min_time*1000:.1f}ms - {max_time*1000:.1f}ms")
                print(f"  Std Dev: {std_dev*1000:.1f}ms")
                
                # Performance assertions
                self.assertGreater(fps, 10, f"{scenario_name} should achieve >10 FPS")
                self.assertLess(avg_time, 0.1, f"{scenario_name} should average <100ms per frame")
            else:
                print(f"! {scenario_name}: No successful captures")
        
        return results
    
    def test_detection_performance_benchmark(self):
        """Benchmark detection performance"""
        detector = NudityDetector()
        
        # Try to load model for realistic benchmarking
        model_loaded = False
        try:
            import nudenet
            model_loaded = detector.load_model()
        except ImportError:
            pass
        
        if not model_loaded:
            print("! Model not available - using preprocessing benchmark only")
        
        import numpy as np
        
        # Test different image sizes
        test_sizes = [
            (320, 240, "Low"),
            (640, 480, "Standard"),
            (1280, 720, "HD"),
        ]
        
        results = {}
        
        for width, height, size_name in test_sizes:
            print(f"\n--- {size_name.upper()} RESOLUTION BENCHMARK ({width}x{height}) ---")
            
            test_image = np.random.randint(0, 255, (height, width, 3), dtype=np.uint8)
            
            if model_loaded:
                # Benchmark full detection
                times = []
                
                # Warm up
                for _ in range(3):
                    detector.detect_nudity(test_image)
                
                # Benchmark
                for i in range(10):
                    start_time = time.time()
                    result = detector.detect_nudity(test_image)
                    end_time = time.time()
                    times.append(end_time - start_time)
                
                avg_time = statistics.mean(times)
                min_time = min(times)
                max_time = max(times)
                fps = 1.0 / avg_time if avg_time > 0 else 0
                
                results[f"{size_name}_detection"] = {
                    'avg_time_ms': avg_time * 1000,
                    'min_time_ms': min_time * 1000,
                    'max_time_ms': max_time * 1000,
                    'fps': fps
                }
                
                print(f"✓ Detection: {avg_time*1000:.1f}ms avg ({fps:.1f} FPS)")
                print(f"  Range: {min_time*1000:.1f}ms - {max_time*1000:.1f}ms")
            else:
                # Benchmark preprocessing only
                times = []
                
                for i in range(20):
                    start_time = time.time()
                    processed = detector.preprocess_frame(test_image)
                    end_time = time.time()
                    times.append(end_time - start_time)
                
                avg_time = statistics.mean(times)
                fps = 1.0 / avg_time if avg_time > 0 else 0
                
                results[f"{size_name}_preprocessing"] = {
                    'avg_time_ms': avg_time * 1000,
                    'fps': fps
                }
                
                print(f"✓ Preprocessing: {avg_time*1000:.1f}ms avg ({fps:.1f} FPS)")
        
        return results
    
    def test_overlay_performance_benchmark(self):
        """Benchmark overlay rendering performance"""
        renderer = OverlayRenderer()
        
        # Setup monitors
        try:
            capture = ScreenCapture()
            monitors = capture.get_monitors()
            success = renderer.setup_monitors_from_monitor_info(monitors)
            
            if not success or len(monitors) == 0:
                self.skipTest("No monitors available for overlay testing")
        except Exception as e:
            self.skipTest(f"Monitor setup failed: {e}")
        
        # Create test detections
        from src.detection.nudity_detector import Detection
        from src.config.settings import BodyPart
        
        test_detections = [
            Detection(BodyPart.BREASTS, 0.9, (0.2, 0.3, 0.4, 0.5), "TEST"),
            Detection(BodyPart.GENITALS, 0.8, (0.6, 0.7, 0.8, 0.9), "TEST"),
        ]
        
        # Benchmark overlay updates
        times = []
        
        print(f"\n--- OVERLAY RENDERING BENCHMARK ---")
        
        for i in range(50):
            start_time = time.time()
            renderer.update_overlays(
                detections=test_detections,
                monitor_index=0,
                frame_shape=(720, 1280)
            )
            end_time = time.time()
            times.append(end_time - start_time)
        
        if times:
            avg_time = statistics.mean(times)
            min_time = min(times)
            max_time = max(times)
            fps = 1.0 / avg_time if avg_time > 0 else 0
            
            print(f"✓ Overlay Update: {avg_time*1000:.1f}ms avg ({fps:.1f} FPS)")
            print(f"  Range: {min_time*1000:.1f}ms - {max_time*1000:.1f}ms")
            
            # Performance assertions
            self.assertLess(avg_time, 0.05, "Overlay updates should be <50ms")
            self.assertGreater(fps, 20, "Overlay updates should achieve >20 FPS")
        
        # Clean up
        renderer.clear_overlays()
    
    def test_memory_usage_benchmark(self):
        """Benchmark memory usage patterns"""
        try:
            import psutil
            process = psutil.Process()
        except ImportError:
            self.skipTest("psutil not available for memory benchmarking")
        
        print(f"\n--- MEMORY USAGE BENCHMARK ---")
        
        # Baseline memory
        baseline_memory = process.memory_info().rss / 1024 / 1024
        print(f"Baseline memory: {baseline_memory:.1f} MB")
        
        # Test component memory usage
        components = {}
        
        # Screen capture
        capture = ScreenCapture()
        capture_memory = process.memory_info().rss / 1024 / 1024
        components['screen_capture'] = capture_memory - baseline_memory
        
        # Detector
        detector = NudityDetector()
        detector_memory = process.memory_info().rss / 1024 / 1024
        components['detector'] = detector_memory - capture_memory
        
        # Overlay renderer
        renderer = OverlayRenderer()
        renderer_memory = process.memory_info().rss / 1024 / 1024
        components['overlay'] = renderer_memory - detector_memory
        
        # Performance manager
        perf_manager = PerformanceManager()
        final_memory = process.memory_info().rss / 1024 / 1024
        components['performance'] = final_memory - renderer_memory
        
        total_increase = final_memory - baseline_memory
        
        print(f"Component memory usage:")
        for component, memory in components.items():
            print(f"  {component}: +{memory:.1f} MB")
        print(f"Total increase: +{total_increase:.1f} MB")
        
        # Memory usage should be reasonable
        self.assertLess(total_increase, 500, "Total memory increase should be <500MB")
        
        return {
            'baseline_mb': baseline_memory,
            'final_mb': final_memory,
            'increase_mb': total_increase,
            'components': components
        }

class TestConcurrentPerformance(unittest.TestCase):
    """Test performance under concurrent load"""
    
    def test_concurrent_capture_performance(self):
        """Test screen capture performance under concurrent load"""
        capture = ScreenCapture()
        
        results = defaultdict(list)
        errors = []
        
        def capture_worker(worker_id, num_captures):
            """Worker function for concurrent capture testing"""
            try:
                times = []
                for i in range(num_captures):
                    start_time = time.time()
                    frame = capture.capture_frame()
                    end_time = time.time()
                    
                    if frame is not None:
                        times.append(end_time - start_time)
                
                results[worker_id] = times
            except Exception as e:
                errors.append(f"Worker {worker_id}: {e}")
        
        print(f"\n--- CONCURRENT CAPTURE BENCHMARK ---")
        
        # Test with multiple threads
        num_workers = 4
        captures_per_worker = 10
        
        threads = []
        start_time = time.time()
        
        for i in range(num_workers):
            thread = threading.Thread(
                target=capture_worker,
                args=(i, captures_per_worker)
            )
            threads.append(thread)
            thread.start()
        
        # Wait for completion
        for thread in threads:
            thread.join(timeout=30)
        
        total_time = time.time() - start_time
        
        # Analyze results
        all_times = []
        successful_workers = 0
        
        for worker_id, times in results.items():
            if times:
                successful_workers += 1
                all_times.extend(times)
                avg_time = statistics.mean(times)
                print(f"  Worker {worker_id}: {len(times)} captures, {avg_time*1000:.1f}ms avg")
        
        if all_times:
            overall_avg = statistics.mean(all_times)
            overall_fps = len(all_times) / total_time
            
            print(f"✓ Concurrent capture: {successful_workers}/{num_workers} workers successful")
            print(f"  Total captures: {len(all_times)}")
            print(f"  Overall average: {overall_avg*1000:.1f}ms")
            print(f"  Overall throughput: {overall_fps:.1f} FPS")
            
            # Performance should degrade gracefully under load
            self.assertGreater(overall_fps, 5, "Concurrent capture should achieve >5 FPS total")
        
        if errors:
            print(f"! Errors: {len(errors)}")
            for error in errors[:3]:
                print(f"  {error}")

def run_performance_benchmarks():
    """Run all performance benchmark tests"""
    print("=" * 60)
    print("PERFORMANCE BENCHMARKS")
    print("=" * 60)

    # Create test suite
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()

    # Add test classes
    suite.addTests(loader.loadTestsFromTestCase(TestSystemPerformance))
    suite.addTests(loader.loadTestsFromTestCase(TestConcurrentPerformance))

    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)

    print("\n" + "=" * 60)
    if result.wasSuccessful():
        print("✓ ALL PERFORMANCE BENCHMARKS PASSED!")
    else:
        print("✗ Some performance benchmarks failed")
        for failure in result.failures:
            print(f"FAILURE: {failure[0]}")
        for error in result.errors:
            print(f"ERROR: {error[0]}")

    print("=" * 60)
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_performance_benchmarks()
    sys.exit(0 if success else 1)
