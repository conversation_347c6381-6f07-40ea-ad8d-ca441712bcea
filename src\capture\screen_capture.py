"""
Screen Capture Module for Censor App
Handles real-time screen capture across multiple monitors
"""

import logging
import time
import threading
from typing import List, Dict, Optional, Tuple, Callable
import numpy as np
from PIL import Image
import mss
import mss.tools
from dataclasses import dataclass

@dataclass
class Monitor:
    """Monitor information"""
    index: int
    x: int
    y: int
    width: int
    height: int
    name: str = ""

@dataclass
class CaptureRegion:
    """Screen capture region"""
    x: int
    y: int
    width: int
    height: int
    monitor_index: int = 0

class ScreenCapture:
    """Real-time screen capture manager"""
    
    def __init__(self, callback: Optional[Callable] = None):
        self.logger = logging.getLogger(__name__)
        self.callback = callback  # Callback function for new frames
        
        # MSS instance for screen capture
        self.sct = mss.mss()
        
        # Monitor information
        self.monitors: List[Monitor] = []
        self.current_monitor = 0
        
        # Capture settings
        self.capture_region: Optional[CaptureRegion] = None
        self.target_fps = 30
        self.max_resolution = (1920, 1080)
        
        # Threading and control
        self.running = False
        self.capture_thread: Optional[threading.Thread] = None
        self.frame_lock = threading.Lock()
        
        # Performance tracking
        self.frame_count = 0
        self.last_fps_time = time.time()
        self.current_fps = 0
        self.capture_times = []
        
        # Initialize monitors
        self._detect_monitors()
        
        self.logger.info(f"Screen capture initialized with {len(self.monitors)} monitors")
    
    def _detect_monitors(self):
        """Detect available monitors"""
        try:
            self.monitors.clear()
            
            # Get monitor information from MSS
            for i, monitor in enumerate(self.sct.monitors):
                if i == 0:  # Skip the "All in One" monitor
                    continue
                
                mon = Monitor(
                    index=i,
                    x=monitor["left"],
                    y=monitor["top"],
                    width=monitor["width"],
                    height=monitor["height"],
                    name=f"Monitor {i}"
                )
                self.monitors.append(mon)
                
                self.logger.info(f"Detected {mon.name}: {mon.width}x{mon.height} at ({mon.x}, {mon.y})")
            
            if not self.monitors:
                # Fallback: create a default monitor
                default_monitor = Monitor(
                    index=1,
                    x=0,
                    y=0,
                    width=1920,
                    height=1080,
                    name="Default Monitor"
                )
                self.monitors.append(default_monitor)
                self.logger.warning("No monitors detected, using default")
                
        except Exception as e:
            self.logger.error(f"Error detecting monitors: {e}")
            # Create fallback monitor
            self.monitors = [Monitor(1, 0, 0, 1920, 1080, "Fallback Monitor")]
    
    def get_monitors(self) -> List[Monitor]:
        """Get list of available monitors"""
        return self.monitors.copy()
    
    def set_monitor(self, monitor_index: int) -> bool:
        """Set the active monitor for capture"""
        if 0 <= monitor_index < len(self.monitors):
            self.current_monitor = monitor_index
            self.capture_region = None  # Reset capture region
            self.logger.info(f"Set active monitor to {self.monitors[monitor_index].name}")
            return True
        else:
            self.logger.error(f"Invalid monitor index: {monitor_index}")
            return False
    
    def set_capture_region(self, region: CaptureRegion) -> bool:
        """Set a specific region for capture"""
        try:
            # Validate region bounds
            if region.monitor_index >= len(self.monitors):
                self.logger.error(f"Invalid monitor index in region: {region.monitor_index}")
                return False
            
            monitor = self.monitors[region.monitor_index]
            
            # Ensure region is within monitor bounds
            if (region.x < 0 or region.y < 0 or 
                region.x + region.width > monitor.width or 
                region.y + region.height > monitor.height):
                self.logger.error("Capture region exceeds monitor bounds")
                return False
            
            self.capture_region = region
            self.current_monitor = region.monitor_index
            self.logger.info(f"Set capture region: {region.width}x{region.height} at ({region.x}, {region.y})")
            return True
            
        except Exception as e:
            self.logger.error(f"Error setting capture region: {e}")
            return False
    
    def clear_capture_region(self):
        """Clear the capture region to capture full monitor"""
        self.capture_region = None
        self.logger.info("Cleared capture region, capturing full monitor")
    
    def set_target_fps(self, fps: int):
        """Set target capture FPS"""
        if 1 <= fps <= 60:
            self.target_fps = fps
            self.logger.info(f"Set target FPS to {fps}")
        else:
            self.logger.error(f"Invalid FPS value: {fps}")
    
    def set_max_resolution(self, width: int, height: int):
        """Set maximum capture resolution"""
        if width > 0 and height > 0:
            self.max_resolution = (width, height)
            self.logger.info(f"Set max resolution to {width}x{height}")
        else:
            self.logger.error(f"Invalid resolution: {width}x{height}")
    
    def capture_frame(self) -> Optional[np.ndarray]:
        """Capture a single frame"""
        try:
            start_time = time.time()
            
            # Determine capture area
            if self.capture_region:
                # Capture specific region
                monitor = self.monitors[self.capture_region.monitor_index]
                capture_area = {
                    "left": monitor.x + self.capture_region.x,
                    "top": monitor.y + self.capture_region.y,
                    "width": self.capture_region.width,
                    "height": self.capture_region.height
                }
            else:
                # Capture full monitor
                if self.current_monitor >= len(self.monitors):
                    self.current_monitor = 0
                
                monitor_info = self.sct.monitors[self.current_monitor + 1]  # +1 because index 0 is "All in One"
                capture_area = monitor_info
            
            # Capture screenshot
            screenshot = self.sct.grab(capture_area)
            
            # Convert to PIL Image
            img = Image.frombytes("RGB", screenshot.size, screenshot.bgra, "raw", "BGRX")
            
            # Resize if necessary
            if (img.width > self.max_resolution[0] or img.height > self.max_resolution[1]):
                img.thumbnail(self.max_resolution, Image.Resampling.LANCZOS)
            
            # Convert to numpy array
            frame = np.array(img)
            
            # Track capture performance
            capture_time = time.time() - start_time
            self.capture_times.append(capture_time)
            if len(self.capture_times) > 100:  # Keep last 100 measurements
                self.capture_times.pop(0)
            
            return frame
            
        except Exception as e:
            self.logger.error(f"Error capturing frame: {e}")
            return None
    
    def start_capture(self) -> bool:
        """Start continuous screen capture"""
        if self.running:
            self.logger.warning("Capture already running")
            return False
        
        try:
            self.running = True
            self.frame_count = 0
            self.last_fps_time = time.time()
            
            # Start capture thread
            self.capture_thread = threading.Thread(target=self._capture_loop, daemon=True)
            self.capture_thread.start()
            
            self.logger.info("Screen capture started")
            return True
            
        except Exception as e:
            self.logger.error(f"Error starting capture: {e}")
            self.running = False
            return False
    
    def stop_capture(self):
        """Stop continuous screen capture"""
        if not self.running:
            return
        
        self.running = False
        
        # Wait for capture thread to finish
        if self.capture_thread and self.capture_thread.is_alive():
            self.capture_thread.join(timeout=2.0)
        
        self.logger.info("Screen capture stopped")
    
    def _capture_loop(self):
        """Main capture loop running in separate thread"""
        frame_interval = 1.0 / self.target_fps
        
        while self.running:
            loop_start = time.time()
            
            try:
                # Capture frame
                frame = self.capture_frame()
                
                if frame is not None:
                    # Update FPS counter
                    self.frame_count += 1
                    current_time = time.time()
                    
                    if current_time - self.last_fps_time >= 1.0:
                        self.current_fps = self.frame_count
                        self.frame_count = 0
                        self.last_fps_time = current_time
                    
                    # Call callback if provided
                    if self.callback:
                        try:
                            self.callback(frame)
                        except Exception as e:
                            self.logger.error(f"Error in capture callback: {e}")
                
                # Maintain target FPS
                elapsed = time.time() - loop_start
                sleep_time = frame_interval - elapsed
                
                if sleep_time > 0:
                    time.sleep(sleep_time)
                elif elapsed > frame_interval * 2:
                    # Log if we're significantly behind
                    self.logger.debug(f"Capture loop running slow: {elapsed:.3f}s per frame")
                    
            except Exception as e:
                self.logger.error(f"Error in capture loop: {e}")
                time.sleep(0.1)  # Brief pause before retrying
    
    def get_performance_stats(self) -> Dict:
        """Get capture performance statistics"""
        avg_capture_time = 0
        if self.capture_times:
            avg_capture_time = sum(self.capture_times) / len(self.capture_times)
        
        return {
            "fps": self.current_fps,
            "target_fps": self.target_fps,
            "avg_capture_time_ms": avg_capture_time * 1000,
            "monitor_count": len(self.monitors),
            "current_monitor": self.current_monitor,
            "capture_region": self.capture_region is not None,
            "running": self.running
        }
    
    def __del__(self):
        """Cleanup when object is destroyed"""
        try:
            self.stop_capture()
            if hasattr(self, 'sct'):
                self.sct.close()
        except:
            pass
