"""
System Integration Tests for Censor App
Tests complete system integration including GUI, system tray, and application lifecycle
"""

import unittest
import sys
import os
import time
import tempfile
import shutil
from unittest.mock import Mock, patch, MagicMock

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

class TestApplicationLifecycle(unittest.TestCase):
    """Test complete application lifecycle"""
    
    def setUp(self):
        """Set up test environment"""
        self.temp_dir = tempfile.mkdtemp()
        self.config_file = os.path.join(self.temp_dir, "test_config.json")
    
    def tearDown(self):
        """Clean up test environment"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_application_initialization(self):
        """Test complete application initialization"""
        try:
            # Test importing main application components
            from src.app_controller import AppController
            from src.config.settings import Settings
            
            # Initialize settings
            settings = Settings(self.config_file)
            self.assertIsNotNone(settings)
            
            # Test AppController initialization (headless mode)
            app_controller = AppController(headless=True)
            self.assertIsNotNone(app_controller)
            
            # Test component initialization
            self.assertIsNotNone(app_controller.settings)
            self.assertIsNotNone(app_controller.screen_capture)
            self.assertIsNotNone(app_controller.detector)
            self.assertIsNotNone(app_controller.overlay_renderer)
            
            print("✓ Application initialization successful")
            
        except ImportError as e:
            self.skipTest(f"Application components not available: {e}")
        except Exception as e:
            self.fail(f"Application initialization failed: {e}")
    
    def test_application_state_management(self):
        """Test application state management"""
        try:
            from src.app_controller import AppController, AppState
            
            app_controller = AppController(headless=True)
            
            # Test initial state
            self.assertEqual(app_controller.get_state(), AppState.STOPPED)
            
            # Test state transitions
            app_controller.start()
            self.assertEqual(app_controller.get_state(), AppState.RUNNING)
            
            app_controller.pause()
            self.assertEqual(app_controller.get_state(), AppState.PAUSED)
            
            app_controller.resume()
            self.assertEqual(app_controller.get_state(), AppState.RUNNING)
            
            app_controller.stop()
            self.assertEqual(app_controller.get_state(), AppState.STOPPED)
            
            print("✓ Application state management working correctly")
            
        except ImportError as e:
            self.skipTest(f"AppController not available: {e}")
        except Exception as e:
            self.fail(f"State management test failed: {e}")
    
    def test_settings_persistence_integration(self):
        """Test settings persistence throughout application lifecycle"""
        try:
            from src.app_controller import AppController
            from src.config.settings import BodyPart, CensorMethod
            
            # Create first instance and modify settings
            app1 = AppController(headless=True, config_file=self.config_file)
            app1.settings.enabled = True
            app1.settings.detection.confidence_threshold = 0.85
            
            breasts_setting = app1.settings.get_body_part_setting(BodyPart.BREASTS)
            breasts_setting.method = CensorMethod.PIXELATION
            breasts_setting.intensity = 0.9
            
            app1.settings.save()
            
            # Create second instance and verify settings persistence
            app2 = AppController(headless=True, config_file=self.config_file)
            
            self.assertTrue(app2.settings.enabled)
            self.assertEqual(app2.settings.detection.confidence_threshold, 0.85)
            
            breasts_setting2 = app2.settings.get_body_part_setting(BodyPart.BREASTS)
            self.assertEqual(breasts_setting2.method, CensorMethod.PIXELATION)
            self.assertEqual(breasts_setting2.intensity, 0.9)
            
            print("✓ Settings persistence integration working correctly")
            
        except ImportError as e:
            self.skipTest(f"AppController not available: {e}")
        except Exception as e:
            self.fail(f"Settings persistence test failed: {e}")

class TestGUIIntegration(unittest.TestCase):
    """Test GUI integration (if available)"""
    
    def setUp(self):
        """Set up test environment"""
        self.gui_available = False
        try:
            import PyQt6
            self.gui_available = True
        except ImportError:
            pass
    
    def test_gui_component_availability(self):
        """Test GUI component availability"""
        if not self.gui_available:
            self.skipTest("PyQt6 not available for GUI testing")
        
        try:
            # Test importing GUI components
            from src.gui.main_window import MainWindow
            from src.gui.settings_dialog import SettingsDialog
            from src.gui.system_tray import SystemTrayManager
            
            print("✓ GUI components available for import")
            
        except ImportError as e:
            print(f"! GUI components not fully available: {e}")
            # This is acceptable as GUI might not be fully implemented
    
    def test_headless_operation(self):
        """Test headless operation without GUI"""
        try:
            from src.app_controller import AppController
            
            # Test headless initialization
            app = AppController(headless=True)
            self.assertIsNotNone(app)
            
            # Test basic operations in headless mode
            app.start()
            time.sleep(0.1)  # Brief operation
            app.stop()
            
            print("✓ Headless operation working correctly")
            
        except ImportError as e:
            self.skipTest(f"AppController not available: {e}")
        except Exception as e:
            self.fail(f"Headless operation test failed: {e}")

class TestSystemTrayIntegration(unittest.TestCase):
    """Test system tray integration"""
    
    def test_system_tray_availability(self):
        """Test system tray component availability"""
        try:
            # Test pystray availability
            import pystray
            from PIL import Image
            
            # Test basic icon creation
            test_image = Image.new('RGB', (16, 16), color='blue')
            icon = pystray.Icon("test", test_image)
            self.assertIsNotNone(icon)
            
            print("✓ System tray components available")
            
        except ImportError as e:
            self.skipTest(f"System tray dependencies not available: {e}")
        except Exception as e:
            print(f"! System tray test error: {e}")

class TestHotkeyIntegration(unittest.TestCase):
    """Test hotkey integration"""
    
    def test_hotkey_system_availability(self):
        """Test hotkey system availability"""
        try:
            # Test keyboard library availability
            import keyboard
            
            # Test basic functionality without actually setting hooks
            self.assertTrue(hasattr(keyboard, 'add_hotkey'))
            self.assertTrue(hasattr(keyboard, 'remove_hotkey'))
            
            print("✓ Hotkey system available")
            
        except ImportError as e:
            self.skipTest(f"Keyboard library not available: {e}")
        except Exception as e:
            print(f"! Hotkey system test error: {e}")
    
    def test_hotkey_manager_integration(self):
        """Test hotkey manager integration"""
        try:
            from src.hotkeys.hotkey_manager import HotkeyManager
            
            # Test hotkey manager initialization
            manager = HotkeyManager()
            self.assertIsNotNone(manager)
            
            # Test basic operations (without actually setting system hooks)
            self.assertFalse(manager.hotkeys_active)
            
            print("✓ Hotkey manager integration working")
            
        except ImportError as e:
            self.skipTest(f"HotkeyManager not available: {e}")
        except Exception as e:
            print(f"! Hotkey manager test error: {e}")

class TestPerformanceIntegration(unittest.TestCase):
    """Test performance manager integration"""
    
    def test_performance_manager_integration(self):
        """Test performance manager integration with other components"""
        try:
            from src.performance.performance_manager import PerformanceManager
            from src.capture.screen_capture import ScreenCapture
            
            # Initialize components
            perf_manager = PerformanceManager()
            screen_capture = ScreenCapture()
            
            # Test performance monitoring
            stats = perf_manager.get_performance_stats()
            self.assertIsInstance(stats, dict)
            
            # Test resource monitoring
            resource_stats = perf_manager.get_resource_usage()
            self.assertIsInstance(resource_stats, dict)
            self.assertIn('cpu_percent', resource_stats)
            self.assertIn('memory_mb', resource_stats)
            
            print("✓ Performance manager integration working")
            
        except ImportError as e:
            self.skipTest(f"Performance components not available: {e}")
        except Exception as e:
            self.fail(f"Performance integration test failed: {e}")

class TestMultiComponentIntegration(unittest.TestCase):
    """Test integration between multiple components"""
    
    def test_capture_detection_overlay_integration(self):
        """Test integration between capture, detection, and overlay components"""
        try:
            from src.capture.screen_capture import ScreenCapture
            from src.detection.nudity_detector import NudityDetector, Detection
            from src.overlay.overlay_renderer import OverlayRenderer
            from src.config.settings import BodyPart
            
            # Initialize components
            capture = ScreenCapture()
            detector = NudityDetector()
            renderer = OverlayRenderer()
            
            # Test monitor setup integration
            monitors = capture.get_monitors()
            self.assertGreater(len(monitors), 0)
            
            success = renderer.setup_monitors_from_monitor_info(monitors)
            self.assertTrue(success)
            
            # Test with mock detection data
            mock_detections = [
                Detection(BodyPart.BREASTS, 0.9, (0.2, 0.3, 0.4, 0.5), "TEST")
            ]
            
            # Test overlay update
            renderer.update_overlays(
                detections=mock_detections,
                monitor_index=0,
                frame_shape=(720, 1280)
            )
            
            print("✓ Multi-component integration working")
            
        except ImportError as e:
            self.skipTest(f"Components not available: {e}")
        except Exception as e:
            self.fail(f"Multi-component integration test failed: {e}")

def run_system_integration_tests():
    """Run all system integration tests"""
    print("=" * 60)
    print("SYSTEM INTEGRATION TESTS")
    print("=" * 60)
    
    # Create test suite
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Add test classes
    suite.addTests(loader.loadTestsFromTestCase(TestApplicationLifecycle))
    suite.addTests(loader.loadTestsFromTestCase(TestGUIIntegration))
    suite.addTests(loader.loadTestsFromTestCase(TestSystemTrayIntegration))
    suite.addTests(loader.loadTestsFromTestCase(TestHotkeyIntegration))
    suite.addTests(loader.loadTestsFromTestCase(TestPerformanceIntegration))
    suite.addTests(loader.loadTestsFromTestCase(TestMultiComponentIntegration))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    print("\n" + "=" * 60)
    if result.wasSuccessful():
        print("✓ ALL SYSTEM INTEGRATION TESTS PASSED!")
    else:
        print("✗ Some system integration tests failed")
        for failure in result.failures:
            print(f"FAILURE: {failure[0]}")
        for error in result.errors:
            print(f"ERROR: {error[0]}")
    
    print("=" * 60)
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_system_integration_tests()
    sys.exit(0 if success else 1)
