"""
Setup script for Censor App
"""

import os
import sys
import subprocess
import platform

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 9):
        print("Error: Python 3.9 or higher is required")
        print(f"Current version: {sys.version}")
        return False
    return True

def check_windows():
    """Check if running on Windows"""
    if platform.system() != "Windows":
        print("Warning: This application is designed for Windows")
        print(f"Current OS: {platform.system()}")
        return False
    return True

def create_virtual_environment():
    """Create a virtual environment"""
    print("Creating virtual environment...")
    try:
        subprocess.run([sys.executable, "-m", "venv", "venv"], check=True)
        print("Virtual environment created successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"Error creating virtual environment: {e}")
        return False

def install_dependencies():
    """Install required dependencies"""
    print("Installing dependencies...")
    
    # Determine pip executable path
    if platform.system() == "Windows":
        pip_path = os.path.join("venv", "Scripts", "pip.exe")
    else:
        pip_path = os.path.join("venv", "bin", "pip")
    
    if not os.path.exists(pip_path):
        print(f"Error: pip not found at {pip_path}")
        return False
    
    try:
        # Upgrade pip first
        subprocess.run([pip_path, "install", "--upgrade", "pip"], check=True)
        
        # Install requirements
        subprocess.run([pip_path, "install", "-r", "requirements.txt"], check=True)
        
        print("Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"Error installing dependencies: {e}")
        return False

def create_directories():
    """Create necessary directories"""
    directories = ["logs", "models", "config"]
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"Created directory: {directory}")

def download_models():
    """Download required AI models"""
    print("Note: AI models will be downloaded automatically on first run")
    print("This may take several minutes depending on your internet connection")

def main():
    """Main setup function"""
    print("Censor App Setup")
    print("================")
    
    # Check requirements
    if not check_python_version():
        return False
    
    if not check_windows():
        response = input("Continue anyway? (y/N): ")
        if response.lower() != 'y':
            return False
    
    # Create virtual environment
    if not os.path.exists("venv"):
        if not create_virtual_environment():
            return False
    else:
        print("Virtual environment already exists")
    
    # Install dependencies
    if not install_dependencies():
        return False
    
    # Create directories
    create_directories()
    
    # Information about models
    download_models()
    
    print("\nSetup completed successfully!")
    print("\nTo run the application:")
    print("1. Activate the virtual environment:")
    print("   venv\\Scripts\\activate")
    print("2. Run the application:")
    print("   python main.py")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
