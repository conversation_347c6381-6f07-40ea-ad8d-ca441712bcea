"""
Main Application Controller for Censor App
Coordinates all components and manages the application lifecycle
"""

import logging
import threading
import time
from typing import Optional
from PyQt6.QtCore import QObject, QTimer, pyqtSignal
from PyQt6.QtWidgets import QApplication

from src.config.settings import Settings
from src.gui.system_tray import SystemTray
from src.gui.main_window import MainWindow


class AppController(QObject):
    """Main application controller that coordinates all components"""
    
    # Signals
    detection_result = pyqtSignal(dict)  # Emitted when detection results are available
    status_changed = pyqtSignal(str)     # Emitted when app status changes
    
    def __init__(self, settings: Settings):
        super().__init__()
        self.settings = settings
        self.logger = logging.getLogger(__name__)
        
        # Component references
        self.system_tray: Optional[SystemTray] = None
        self.main_window: Optional[MainWindow] = None
        
        # Core components (will be initialized in later tasks)
        self.screen_capture = None
        self.detection_engine = None
        self.overlay_renderer = None
        
        # Threading and control
        self.running = False
        self.main_thread: Optional[threading.Thread] = None
        self.processing_timer: Optional[QTimer] = None
        
        # Performance tracking
        self.fps_counter = 0
        self.last_fps_time = time.time()
        self.current_fps = 0
        
        self._setup_components()
    
    def _setup_components(self):
        """Initialize all application components"""
        try:
            # Initialize GUI components
            self.system_tray = SystemTray(self)
            self.main_window = MainWindow(self)
            
            # Connect signals
            self._connect_signals()
            
            self.logger.info("Application components initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Error setting up components: {e}")
            raise
    
    def _connect_signals(self):
        """Connect signals between components"""
        # Connect system tray signals
        if self.system_tray:
            self.system_tray.toggle_requested.connect(self.toggle_censoring)
            self.system_tray.settings_requested.connect(self.show_settings)
            self.system_tray.quit_requested.connect(self.stop)
        
        # Connect main window signals
        if self.main_window:
            self.main_window.settings_changed.connect(self.on_settings_changed)
        
        # Connect status updates
        self.status_changed.connect(self._update_status_display)
    
    def start(self):
        """Start the application"""
        try:
            self.logger.info("Starting Censor App...")
            
            # Show system tray
            if self.system_tray:
                self.system_tray.show()
            
            # Initialize processing timer for future use
            self.processing_timer = QTimer()
            self.processing_timer.timeout.connect(self._process_frame)
            
            # Start censoring if enabled in settings
            if self.settings.enabled:
                self.start_censoring()
            
            self.status_changed.emit("Application started")
            self.logger.info("Censor App started successfully")
            
        except Exception as e:
            self.logger.error(f"Error starting application: {e}")
            raise
    
    def stop(self):
        """Stop the application"""
        try:
            self.logger.info("Stopping Censor App...")
            
            # Stop censoring
            self.stop_censoring()
            
            # Hide system tray
            if self.system_tray:
                self.system_tray.hide()
            
            # Close main window
            if self.main_window:
                self.main_window.close()
            
            # Quit application
            QApplication.quit()
            
            self.status_changed.emit("Application stopped")
            self.logger.info("Censor App stopped successfully")
            
        except Exception as e:
            self.logger.error(f"Error stopping application: {e}")
    
    def start_censoring(self):
        """Start the censoring process"""
        if self.running:
            return
        
        try:
            self.logger.info("Starting censoring process...")
            self.running = True
            
            # TODO: Initialize capture, detection, and overlay components
            # This will be implemented in subsequent tasks
            
            # Start processing timer (placeholder for now)
            if self.processing_timer:
                # Start at reduced frequency until we have real processing
                self.processing_timer.start(100)  # 10 FPS placeholder
            
            self.status_changed.emit("Censoring active")
            self.logger.info("Censoring process started")
            
        except Exception as e:
            self.logger.error(f"Error starting censoring: {e}")
            self.running = False
    
    def stop_censoring(self):
        """Stop the censoring process"""
        if not self.running:
            return
        
        try:
            self.logger.info("Stopping censoring process...")
            self.running = False
            
            # Stop processing timer
            if self.processing_timer:
                self.processing_timer.stop()
            
            # TODO: Stop capture, detection, and overlay components
            # This will be implemented in subsequent tasks
            
            self.status_changed.emit("Censoring inactive")
            self.logger.info("Censoring process stopped")
            
        except Exception as e:
            self.logger.error(f"Error stopping censoring: {e}")
    
    def toggle_censoring(self):
        """Toggle censoring on/off"""
        if self.running:
            self.stop_censoring()
            self.settings.enabled = False
        else:
            self.start_censoring()
            self.settings.enabled = True
        
        self.settings.save()
        return self.running
    
    def show_settings(self):
        """Show the settings window"""
        if self.main_window:
            self.main_window.show()
            self.main_window.raise_()
            self.main_window.activateWindow()
    
    def on_settings_changed(self):
        """Handle settings changes"""
        self.logger.info("Settings changed, applying updates...")
        
        # Restart censoring if it was running to apply new settings
        if self.running:
            self.stop_censoring()
            self.start_censoring()
    
    def _process_frame(self):
        """Process a single frame (placeholder for now)"""
        # This is a placeholder that will be implemented when we add
        # the actual capture, detection, and overlay components
        
        # Update FPS counter
        self.fps_counter += 1
        current_time = time.time()
        if current_time - self.last_fps_time >= 1.0:
            self.current_fps = self.fps_counter
            self.fps_counter = 0
            self.last_fps_time = current_time
            
            # Log FPS occasionally for debugging
            if self.current_fps > 0:
                self.logger.debug(f"Processing FPS: {self.current_fps}")
    
    def _update_status_display(self, status: str):
        """Update status display in GUI components"""
        if self.system_tray:
            self.system_tray.update_status(status)
        
        if self.main_window and self.main_window.isVisible():
            self.main_window.update_status(status)
    
    def get_status(self) -> dict:
        """Get current application status"""
        return {
            'running': self.running,
            'enabled': self.settings.enabled,
            'fps': self.current_fps,
            'components': {
                'capture': self.screen_capture is not None,
                'detection': self.detection_engine is not None,
                'overlay': self.overlay_renderer is not None
            }
        }
