"""
Main Application Controller for Censor App
Coordinates all components and manages the application lifecycle
"""

import logging
import threading
import time
from typing import Optional
from PyQt6.QtCore import QObject, QTimer, pyqtSignal
from PyQt6.QtWidgets import QApplication

from src.config.settings import Settings
from src.gui.system_tray import SystemTray
from src.gui.main_window import MainWindow
from src.capture.screen_capture import ScreenCapture
from src.detection.nudity_detector import NudityDetector


class AppController(QObject):
    """Main application controller that coordinates all components"""
    
    # Signals
    detection_result = pyqtSignal(dict)  # Emitted when detection results are available
    status_changed = pyqtSignal(str)     # Emitted when app status changes
    
    def __init__(self, settings: Settings):
        super().__init__()
        self.settings = settings
        self.logger = logging.getLogger(__name__)
        
        # Component references
        self.system_tray: Optional[SystemTray] = None
        self.main_window: Optional[MainWindow] = None
        
        # Core components
        self.screen_capture: Optional[ScreenCapture] = None
        self.detection_engine: Optional[NudityDetector] = None
        self.overlay_renderer = None
        
        # Threading and control
        self.running = False
        self.main_thread: Optional[threading.Thread] = None
        self.processing_timer: Optional[QTimer] = None
        
        # Performance tracking
        self.fps_counter = 0
        self.last_fps_time = time.time()
        self.current_fps = 0
        
        self._setup_components()
    
    def _setup_components(self):
        """Initialize all application components"""
        try:
            # Initialize GUI components
            self.system_tray = SystemTray(self)
            self.main_window = MainWindow(self)

            # Initialize screen capture
            self.screen_capture = ScreenCapture(callback=self._on_frame_captured)
            self.screen_capture.set_target_fps(self.settings.performance.max_fps)
            self.screen_capture.set_max_resolution(self.settings.performance.max_resolution)

            # Initialize detection engine
            self.detection_engine = NudityDetector()
            self.detection_engine.set_confidence_threshold(self.settings.detection.confidence_threshold)
            self.detection_engine.set_input_size(*self.settings.performance.detection_resolution)

            # Connect signals
            self._connect_signals()

            self.logger.info("Application components initialized successfully")

        except Exception as e:
            self.logger.error(f"Error setting up components: {e}")
            raise
    
    def _connect_signals(self):
        """Connect signals between components"""
        # Connect system tray signals
        if self.system_tray:
            self.system_tray.toggle_requested.connect(self.toggle_censoring)
            self.system_tray.settings_requested.connect(self.show_settings)
            self.system_tray.quit_requested.connect(self.stop)
        
        # Connect main window signals
        if self.main_window:
            self.main_window.settings_changed.connect(self.on_settings_changed)
        
        # Connect status updates
        self.status_changed.connect(self._update_status_display)
    
    def start(self):
        """Start the application"""
        try:
            self.logger.info("Starting Censor App...")
            
            # Show system tray
            if self.system_tray:
                self.system_tray.show()
            
            # Initialize processing timer for future use
            self.processing_timer = QTimer()
            self.processing_timer.timeout.connect(self._process_frame)
            
            # Start censoring if enabled in settings
            if self.settings.enabled:
                self.start_censoring()
            
            self.status_changed.emit("Application started")
            self.logger.info("Censor App started successfully")
            
        except Exception as e:
            self.logger.error(f"Error starting application: {e}")
            raise
    
    def stop(self):
        """Stop the application"""
        try:
            self.logger.info("Stopping Censor App...")
            
            # Stop censoring
            self.stop_censoring()
            
            # Hide system tray
            if self.system_tray:
                self.system_tray.hide()
            
            # Close main window
            if self.main_window:
                self.main_window.close()
            
            # Quit application
            QApplication.quit()
            
            self.status_changed.emit("Application stopped")
            self.logger.info("Censor App stopped successfully")
            
        except Exception as e:
            self.logger.error(f"Error stopping application: {e}")
    
    def start_censoring(self):
        """Start the censoring process"""
        if self.running:
            return

        try:
            self.logger.info("Starting censoring process...")
            self.running = True

            # Start screen capture
            if self.screen_capture:
                capture_started = self.screen_capture.start_capture()
                if not capture_started:
                    self.logger.error("Failed to start screen capture")
                    self.running = False
                    return

            # Start processing timer for performance monitoring
            if self.processing_timer:
                self.processing_timer.start(1000)  # Update stats every second

            self.status_changed.emit("Censoring active")
            self.logger.info("Censoring process started")

        except Exception as e:
            self.logger.error(f"Error starting censoring: {e}")
            self.running = False
    
    def stop_censoring(self):
        """Stop the censoring process"""
        if not self.running:
            return

        try:
            self.logger.info("Stopping censoring process...")
            self.running = False

            # Stop screen capture
            if self.screen_capture:
                self.screen_capture.stop_capture()

            # Stop processing timer
            if self.processing_timer:
                self.processing_timer.stop()

            self.status_changed.emit("Censoring inactive")
            self.logger.info("Censoring process stopped")

        except Exception as e:
            self.logger.error(f"Error stopping censoring: {e}")
    
    def toggle_censoring(self):
        """Toggle censoring on/off"""
        if self.running:
            self.stop_censoring()
            self.settings.enabled = False
        else:
            self.start_censoring()
            self.settings.enabled = True
        
        self.settings.save()
        return self.running
    
    def show_settings(self):
        """Show the settings window"""
        if self.main_window:
            self.main_window.show()
            self.main_window.raise_()
            self.main_window.activateWindow()
    
    def on_settings_changed(self):
        """Handle settings changes"""
        self.logger.info("Settings changed, applying updates...")
        
        # Restart censoring if it was running to apply new settings
        if self.running:
            self.stop_censoring()
            self.start_censoring()
    
    def _process_frame(self):
        """Process performance monitoring and stats update"""
        if self.screen_capture and self.running:
            # Get capture performance stats
            stats = self.screen_capture.get_performance_stats()
            self.current_fps = stats.get('fps', 0)

            # Log performance occasionally
            if self.current_fps > 0:
                self.logger.debug(f"Capture FPS: {self.current_fps}, Target: {stats.get('target_fps', 0)}")

    def _on_frame_captured(self, frame):
        """Callback for when a new frame is captured"""
        try:
            # Count frames for performance tracking
            self.fps_counter += 1

            # Skip processing if censoring is disabled
            if not self.settings.enabled:
                return

            # Run nudity detection
            if self.detection_engine:
                detection_result = self.detection_engine.detect_nudity(frame)

                # Filter detections based on enabled body parts
                enabled_parts = self.settings.get_enabled_body_parts()
                filtered_detections = self.detection_engine.filter_detections_by_body_parts(
                    detection_result.detections, enabled_parts
                )

                # TODO: Add overlay rendering here in subsequent task
                # For now, just log detection results
                if filtered_detections:
                    self.logger.debug(f"Detected {len(filtered_detections)} items requiring censoring")

        except Exception as e:
            self.logger.error(f"Error processing captured frame: {e}")
    
    def _update_status_display(self, status: str):
        """Update status display in GUI components"""
        if self.system_tray:
            self.system_tray.update_status(status)
        
        if self.main_window and self.main_window.isVisible():
            self.main_window.update_status(status)
    
    def get_status(self) -> dict:
        """Get current application status"""
        capture_stats = {}
        if self.screen_capture:
            capture_stats = self.screen_capture.get_performance_stats()

        detection_stats = {}
        if self.detection_engine:
            detection_stats = self.detection_engine.get_performance_stats()

        return {
            'running': self.running,
            'enabled': self.settings.enabled,
            'fps': self.current_fps,
            'capture_stats': capture_stats,
            'detection_stats': detection_stats,
            'components': {
                'capture': self.screen_capture is not None,
                'detection': self.detection_engine is not None,
                'overlay': self.overlay_renderer is not None
            }
        }

    def get_monitors(self):
        """Get available monitors for capture"""
        if self.screen_capture:
            return self.screen_capture.get_monitors()
        return []

    def set_capture_monitor(self, monitor_index: int) -> bool:
        """Set the active monitor for capture"""
        if self.screen_capture:
            return self.screen_capture.set_monitor(monitor_index)
        return False

    def load_detection_model(self) -> bool:
        """Load the detection model"""
        if self.detection_engine:
            return self.detection_engine.load_model()
        return False

    def set_detection_confidence(self, confidence: float) -> bool:
        """Set detection confidence threshold"""
        if self.detection_engine:
            self.detection_engine.set_confidence_threshold(confidence)
            # Also update settings
            self.settings.detection.confidence_threshold = confidence
            self.settings.save()
            return True
        return False
