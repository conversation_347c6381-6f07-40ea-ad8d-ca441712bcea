"""
Main Application Controller for Censor App
Coordinates all components and manages the application lifecycle
"""

import logging
import threading
import time
import queue
import sys
import os
from typing import Optional, Dict, Any
from enum import Enum
from PyQt6.QtCore import QObject, QTimer, pyqtSignal, QThread, QMutex, QWaitCondition
from PyQt6.QtWidgets import QApplication

from src.config.settings import Settings, BodyPart, CensorSettings, CensorMethod
from src.gui.system_tray import SystemTray
from src.gui.main_window import MainWindow
from src.capture.screen_capture import ScreenCapture
from src.detection.nudity_detector import NudityDetector
from src.overlay.overlay_renderer import OverlayRenderer
from src.hotkeys.hotkey_manager import HotkeyManager, HotkeyAction
from src.performance.performance_manager import PerformanceManager


class AppState(Enum):
    """Application state enumeration"""
    STOPPED = "stopped"
    STARTING = "starting"
    RUNNING = "running"
    STOPPING = "stopping"
    ERROR = "error"


class ProcessingThread(QThread):
    """Dedicated thread for frame processing"""

    # Signals for thread communication
    frame_processed = pyqtSignal(object)  # Processed frame data
    error_occurred = pyqtSignal(str)      # Error message
    stats_updated = pyqtSignal(dict)      # Performance statistics

    def __init__(self, controller):
        super().__init__()
        self.controller = controller
        self.logger = logging.getLogger(__name__)

        # Thread control
        self.running = False
        self.mutex = QMutex()
        self.wait_condition = QWaitCondition()

        # Frame queue for processing
        self.frame_queue = queue.Queue(maxsize=10)  # Limit queue size

        # Performance tracking
        self.frames_processed = 0
        self.processing_times = []
        self.last_stats_time = time.time()

    def run(self):
        """Main thread processing loop"""
        self.logger.info("Processing thread started")
        self.running = True

        while self.running:
            try:
                # Get frame from queue (with timeout)
                try:
                    frame_data = self.frame_queue.get(timeout=0.1)
                except queue.Empty:
                    continue

                # Process frame
                start_time = time.time()
                self._process_frame(frame_data)
                processing_time = time.time() - start_time

                # Update performance stats
                self.frames_processed += 1
                self.processing_times.append(processing_time)

                # Emit stats periodically
                if time.time() - self.last_stats_time > 1.0:
                    self._emit_stats()
                    self.last_stats_time = time.time()

                # Mark frame as processed
                self.frame_queue.task_done()

            except Exception as e:
                self.logger.error(f"Error in processing thread: {e}")
                self.error_occurred.emit(str(e))

        self.logger.info("Processing thread stopped")

    def _process_frame(self, frame_data):
        """Process a single frame"""
        frame, timestamp, monitor_index = frame_data

        # Delegate to controller for actual processing
        if self.controller and hasattr(self.controller, '_process_frame_internal'):
            result = self.controller._process_frame_internal(frame, timestamp, monitor_index)
            self.frame_processed.emit(result)

    def _emit_stats(self):
        """Emit performance statistics"""
        if not self.processing_times:
            return

        avg_time = sum(self.processing_times) / len(self.processing_times)
        max_time = max(self.processing_times)
        fps = 1.0 / avg_time if avg_time > 0 else 0

        stats = {
            'frames_processed': self.frames_processed,
            'avg_processing_time': avg_time,
            'max_processing_time': max_time,
            'processing_fps': fps,
            'queue_size': self.frame_queue.qsize()
        }

        self.stats_updated.emit(stats)

        # Keep only recent processing times
        if len(self.processing_times) > 100:
            self.processing_times = self.processing_times[-50:]

    def add_frame(self, frame, timestamp, monitor_index=0):
        """Add frame to processing queue"""
        if not self.running:
            return False

        try:
            # Add frame to queue (non-blocking)
            self.frame_queue.put_nowait((frame, timestamp, monitor_index))
            return True
        except queue.Full:
            # Queue is full, drop frame
            self.logger.warning("Processing queue full, dropping frame")
            return False

    def start_processing(self):
        """Start the processing thread"""
        self.running = True
        self.logger.info("Processing thread ready to start")

    def stop_processing(self):
        """Stop the processing thread"""
        self.running = False
        self.logger.info("Processing thread stop requested")


class AppController(QObject):
    """Main application controller that coordinates all components"""
    
    # Signals
    detection_result = pyqtSignal(dict)  # Emitted when detection results are available
    status_changed = pyqtSignal(str)     # Emitted when app status changes
    state_changed = pyqtSignal(str)      # Application state changes
    error_occurred = pyqtSignal(str)     # Error notifications

    def __init__(self, settings: Settings, headless: bool = False):
        super().__init__()
        self.settings = settings
        self.headless = headless
        self.logger = logging.getLogger(__name__)

        # Application state management
        self.app_state = AppState.STOPPED
        self.state_lock = threading.Lock()
        self.startup_error = None

        # Component references
        self.system_tray: Optional[SystemTray] = None
        self.main_window: Optional[MainWindow] = None

        # Core components
        self.screen_capture: Optional[ScreenCapture] = None
        self.detection_engine: Optional[NudityDetector] = None
        self.overlay_renderer: Optional[OverlayRenderer] = None
        self.hotkey_manager: Optional[HotkeyManager] = None
        self.performance_manager: Optional[PerformanceManager] = None

        # Enhanced threading and control
        self.running = False
        self.main_thread: Optional[threading.Thread] = None
        self.processing_thread: Optional[ProcessingThread] = None
        self.processing_timer: Optional[QTimer] = None

        # Enhanced performance tracking
        self.fps_counter = 0
        self.last_fps_time = time.time()
        self.current_fps = 0
        self.performance_stats = {
            'capture_fps': 0.0,
            'processing_fps': 0.0,
            'detection_time': 0.0,
            'overlay_time': 0.0,
            'total_frames': 0,
            'dropped_frames': 0,
            'queue_size': 0
        }
        
        self._setup_components()
    
    def _setup_components(self):
        """Initialize all application components"""
        try:
            self._set_state(AppState.STARTING)

            # Initialize GUI components (unless headless)
            if not self.headless:
                self.system_tray = SystemTray(self)
                self.main_window = MainWindow(self)
                self.logger.info("GUI components initialized")

            # Initialize screen capture
            self.screen_capture = ScreenCapture(callback=self._on_frame_captured)
            self.screen_capture.set_target_fps(self.settings.performance.max_fps)
            max_res = self.settings.performance.max_resolution
            self.screen_capture.set_max_resolution(max_res[0], max_res[1])
            self.logger.info("Screen capture initialized")

            # Initialize performance manager
            self.performance_manager = PerformanceManager(
                self.settings.performance,
                self.settings.detection
            )
            self.logger.info("Performance manager initialized")

            # Initialize detection engine
            self.detection_engine = NudityDetector(performance_manager=self.performance_manager)
            self.detection_engine.set_confidence_threshold(self.settings.detection.confidence_threshold)
            # Use default detection resolution of 416x416 (common for YOLO models)
            detection_resolution = getattr(self.settings.detection, 'input_size', (416, 416))
            self.detection_engine.set_input_size(*detection_resolution)
            self.logger.info("Detection engine initialized")

            # Initialize overlay renderer
            self.overlay_renderer = OverlayRenderer()
            self._setup_overlay_monitors()
            self.logger.info("Overlay renderer initialized")

            # Initialize processing thread
            self.processing_thread = ProcessingThread(self)
            self.processing_thread.frame_processed.connect(self._on_frame_processed)
            self.processing_thread.error_occurred.connect(self._on_processing_error)
            self.processing_thread.stats_updated.connect(self._on_processing_stats)
            self.logger.info("Processing thread initialized")

            # Initialize hotkey manager
            self.hotkey_manager = HotkeyManager()
            self._setup_hotkeys()
            self.logger.info("Hotkey manager initialized")

            self._set_state(AppState.STOPPED)
            self.logger.info("All components initialized successfully")

            # Connect signals
            self._connect_signals()

            self.logger.info("Application components initialized successfully")

        except Exception as e:
            self.logger.error(f"Error setting up components: {e}")
            raise

    def _setup_hotkeys(self):
        """Setup global hotkeys"""
        if not self.hotkey_manager or not self.hotkey_manager.is_available():
            self.logger.warning("Hotkey manager not available - skipping hotkey setup")
            return

        try:
            # Basic controls
            self.hotkey_manager.register_hotkey(
                HotkeyAction.TOGGLE_CENSORING,
                self.settings.hotkeys.toggle_censoring,
                self._hotkey_toggle_censoring,
                "Toggle censoring on/off"
            )

            self.hotkey_manager.register_hotkey(
                HotkeyAction.TOGGLE_BREASTS,
                self.settings.hotkeys.toggle_breasts,
                self._hotkey_toggle_breasts,
                "Toggle breast censoring"
            )

            self.hotkey_manager.register_hotkey(
                HotkeyAction.TOGGLE_GENITALS,
                self.settings.hotkeys.toggle_genitals,
                self._hotkey_toggle_genitals,
                "Toggle genital censoring"
            )

            self.hotkey_manager.register_hotkey(
                HotkeyAction.TOGGLE_BUTTOCKS,
                self.settings.hotkeys.toggle_buttocks,
                self._hotkey_toggle_buttocks,
                "Toggle buttock censoring"
            )

            self.hotkey_manager.register_hotkey(
                HotkeyAction.EMERGENCY_DISABLE,
                self.settings.hotkeys.emergency_disable,
                self._hotkey_emergency_disable,
                "Emergency disable all censoring"
            )

            # Advanced controls
            self.hotkey_manager.register_hotkey(
                HotkeyAction.QUICK_PRESET_MINIMAL,
                self.settings.hotkeys.quick_preset_minimal,
                self._hotkey_preset_minimal,
                "Apply minimal censoring preset"
            )

            self.hotkey_manager.register_hotkey(
                HotkeyAction.QUICK_PRESET_MODERATE,
                self.settings.hotkeys.quick_preset_moderate,
                self._hotkey_preset_moderate,
                "Apply moderate censoring preset"
            )

            self.hotkey_manager.register_hotkey(
                HotkeyAction.QUICK_PRESET_MAXIMUM,
                self.settings.hotkeys.quick_preset_maximum,
                self._hotkey_preset_maximum,
                "Apply maximum censoring preset"
            )

            self.hotkey_manager.register_hotkey(
                HotkeyAction.TEMPORARY_DISABLE,
                self.settings.hotkeys.temporary_disable,
                self._hotkey_temporary_disable,
                "Temporarily disable censoring"
            )

            self.hotkey_manager.register_hotkey(
                HotkeyAction.STEALTH_MODE,
                self.settings.hotkeys.stealth_mode,
                self._hotkey_stealth_mode,
                "Toggle stealth mode"
            )

            self.hotkey_manager.register_hotkey(
                HotkeyAction.INCREASE_INTENSITY,
                self.settings.hotkeys.increase_intensity,
                self._hotkey_increase_intensity,
                "Increase censoring intensity"
            )

            self.hotkey_manager.register_hotkey(
                HotkeyAction.DECREASE_INTENSITY,
                self.settings.hotkeys.decrease_intensity,
                self._hotkey_decrease_intensity,
                "Decrease censoring intensity"
            )

            # Enable hotkeys if configured
            self.hotkey_manager.enable_all(self.settings.hotkeys.enabled)

            self.logger.info("Hotkeys registered successfully")

        except Exception as e:
            self.logger.error(f"Error setting up hotkeys: {e}")
    
    def _connect_signals(self):
        """Connect signals between components"""
        # Connect system tray signals
        if self.system_tray:
            self.system_tray.toggle_requested.connect(self.toggle_censoring)
            self.system_tray.settings_requested.connect(self.show_settings)
            self.system_tray.quit_requested.connect(self.stop)
        
        # Connect main window signals
        if self.main_window:
            self.main_window.settings_changed.connect(self.on_settings_changed)
        
        # Connect status updates
        self.status_changed.connect(self._update_status_display)
    
    def start(self):
        """Start the application"""
        try:
            self.logger.info("Starting Censor App...")

            # Show system tray
            if self.system_tray:
                self.system_tray.show()

            # Initialize processing timer for future use
            self.processing_timer = QTimer()
            self.processing_timer.timeout.connect(self._process_frame)

            # Start censoring if enabled in settings
            if self.settings.enabled:
                self.start_censoring()
            else:
                self._set_state(AppState.STOPPED)

            self.status_changed.emit("Application started")
            self.logger.info("Censor App started successfully")

        except Exception as e:
            self.logger.error(f"Error starting application: {e}")
            self._set_state(AppState.ERROR)
            self.error_occurred.emit(f"Error starting application: {e}")
            raise
    
    def stop(self):
        """Stop the application"""
        try:
            self.logger.info("Stopping Censor App...")
            
            # Stop censoring
            self.stop_censoring()
            
            # Hide system tray
            if self.system_tray:
                self.system_tray.hide()
            
            # Close main window
            if self.main_window:
                self.main_window.close()
            
            # Quit application
            QApplication.quit()
            
            self.status_changed.emit("Application stopped")
            self.logger.info("Censor App stopped successfully")
            
        except Exception as e:
            self.logger.error(f"Error stopping application: {e}")

    def _set_state(self, new_state: AppState):
        """Thread-safe state management"""
        with self.state_lock:
            if self.app_state != new_state:
                old_state = self.app_state
                self.app_state = new_state
                self.logger.info(f"State changed: {old_state.value} -> {new_state.value}")
                self.state_changed.emit(new_state.value)

    def get_state(self) -> AppState:
        """Get current application state"""
        with self.state_lock:
            return self.app_state

    def start_censoring(self):
        """Start the censoring process"""
        if self.running:
            return

        try:
            self.logger.info("Starting censoring process...")
            self._set_state(AppState.STARTING)

            # Start processing thread first
            if self.processing_thread and not self.processing_thread.isRunning():
                self.processing_thread.start_processing()
                self.processing_thread.start()
                self.logger.info("Processing thread started")

            # Start screen capture
            if self.screen_capture:
                capture_started = self.screen_capture.start_capture()
                if not capture_started:
                    self.logger.error("Failed to start screen capture")
                    self._set_state(AppState.ERROR)
                    self.running = False
                    self.error_occurred.emit("Failed to start screen capture")
                    return

            # Start processing timer for performance monitoring
            if self.processing_timer:
                self.processing_timer.start(1000)  # Update stats every second

            self.running = True
            self._set_state(AppState.RUNNING)
            self.status_changed.emit("Censoring active")
            self.logger.info("Censoring process started")

        except Exception as e:
            self.logger.error(f"Error starting censoring: {e}")
            self.running = False
            self._set_state(AppState.ERROR)
            self.error_occurred.emit(f"Error starting censoring: {e}")
    
    def stop_censoring(self):
        """Stop the censoring process"""
        if not self.running:
            return

        try:
            self.logger.info("Stopping censoring process...")
            self._set_state(AppState.STOPPING)
            self.running = False

            # Stop screen capture
            if self.screen_capture:
                self.screen_capture.stop_capture()

            # Stop processing thread
            if self.processing_thread and self.processing_thread.isRunning():
                self.processing_thread.stop_processing()
                self.processing_thread.wait(2000)  # Wait up to 2 seconds
                self.logger.info("Processing thread stopped")

            # Stop processing timer
            if self.processing_timer:
                self.processing_timer.stop()

            # Clear any remaining overlays
            if self.overlay_renderer:
                self.overlay_renderer.clear_overlays()

            self._set_state(AppState.STOPPED)
            self.status_changed.emit("Censoring inactive")
            self.logger.info("Censoring process stopped")

        except Exception as e:
            self.logger.error(f"Error stopping censoring: {e}")
            self._set_state(AppState.ERROR)
            self.error_occurred.emit(f"Error stopping censoring: {e}")

    def _process_frame_internal(self, frame, timestamp, monitor_index=0):
        """Internal frame processing method for threading"""
        try:
            start_time = time.time()

            # Check if frame should be processed (frame skipping optimization)
            if self.performance_manager and not self.performance_manager.should_process_frame():
                return None

            # Update frame count and FPS
            self.fps_counter += 1
            current_time = time.time()
            if current_time - self.last_fps_time >= 1.0:
                self.current_fps = self.fps_counter / (current_time - self.last_fps_time)
                self.fps_counter = 0
                self.last_fps_time = current_time
                self.performance_stats['capture_fps'] = self.current_fps

            # Skip processing if not enabled
            if not self.settings.enabled:
                return None

            detection_start = time.time()
            detection_result = None
            filtered_detections = []

            # Run detection if engine is available
            if self.detection_engine:
                detection_result = self.detection_engine.detect_nudity(frame)
                enabled_parts = self.settings.get_enabled_body_parts()
                filtered_detections = self.detection_engine.filter_detections_by_body_parts(
                    detection_result.detections, enabled_parts
                )

            detection_time = time.time() - detection_start
            self.performance_stats['detection_time'] = detection_time

            # Apply overlay rendering
            overlay_start = time.time()
            if filtered_detections and self.overlay_renderer:
                # Get censor settings for each body part
                censor_settings = {
                    part: self.settings.get_body_part_setting(part)
                    for part in [BodyPart.BREASTS, BodyPart.GENITALS, BodyPart.BUTTOCKS]
                }

                # Update overlays
                self.overlay_renderer.update_overlays(
                    filtered_detections,
                    monitor_index=monitor_index,
                    frame_shape=frame.shape[:2],
                    censor_settings=censor_settings
                )

                # Show overlays if not visible
                if not self.overlay_renderer.visible:
                    self.overlay_renderer.show_overlays()

            elif self.overlay_renderer and self.overlay_renderer.visible:
                # Hide overlays if no detections
                self.overlay_renderer.hide_overlays()

            overlay_time = time.time() - overlay_start
            self.performance_stats['overlay_time'] = overlay_time
            self.performance_stats['total_frames'] += 1

            # Update performance manager metrics
            if self.performance_manager:
                total_processing_time = time.time() - start_time
                self.performance_manager.update_performance_metrics(
                    processing_time=total_processing_time,
                    detection_time=detection_time,
                    overlay_time=overlay_time,
                    frames_processed=self.performance_stats['total_frames'],
                    frames_dropped=self.performance_stats['dropped_frames']
                )

            # Emit detection result signal
            if detection_result:
                self.detection_result.emit({
                    'detections': len(filtered_detections),
                    'timestamp': timestamp,
                    'monitor': monitor_index,
                    'processing_time': time.time() - start_time
                })

            return {
                'detections': filtered_detections,
                'processing_time': time.time() - start_time,
                'timestamp': timestamp
            }

        except Exception as e:
            self.logger.error(f"Error processing frame: {e}")
            self.performance_stats['dropped_frames'] += 1
            return None

    def _on_frame_processed(self, result):
        """Handle processed frame results from processing thread"""
        if result:
            # Update performance stats or handle results as needed
            pass

    def _on_processing_error(self, error_msg):
        """Handle processing thread errors"""
        self.logger.error(f"Processing thread error: {error_msg}")
        self.error_occurred.emit(error_msg)

    def _on_processing_stats(self, stats):
        """Handle processing thread statistics"""
        self.performance_stats.update(stats)

    def toggle_censoring(self):
        """Toggle censoring on/off"""
        try:
            if self.running:
                self.stop_censoring()
                self.settings.enabled = False
            else:
                self.start_censoring()
                self.settings.enabled = True

            self.settings.save()
            return self.running
        except Exception as e:
            self.logger.error(f"Error toggling censoring: {e}")
            self.error_occurred.emit(f"Error toggling censoring: {e}")
            return False
    
    def show_settings(self):
        """Show the settings window"""
        if self.main_window:
            self.main_window.show()
            self.main_window.raise_()
            self.main_window.activateWindow()
    
    def on_settings_changed(self):
        """Handle settings changes"""
        self.logger.info("Settings changed, applying updates...")
        
        # Restart censoring if it was running to apply new settings
        if self.running:
            self.stop_censoring()
            self.start_censoring()
    
    def _process_frame(self):
        """Process performance monitoring and stats update"""
        if self.screen_capture and self.running:
            # Get capture performance stats
            stats = self.screen_capture.get_performance_stats()
            self.current_fps = stats.get('fps', 0)

            # Log performance occasionally
            if self.current_fps > 0:
                self.logger.debug(f"Capture FPS: {self.current_fps}, Target: {stats.get('target_fps', 0)}")

    def _on_frame_captured(self, frame):
        """Enhanced callback for when a frame is captured - uses threading"""
        try:
            # Count frames for performance tracking
            self.fps_counter += 1
            timestamp = time.time()

            # Skip processing if censoring is disabled
            if not self.settings.enabled:
                return

            # Add frame to processing queue if thread is available
            if self.processing_thread and self.processing_thread.isRunning():
                success = self.processing_thread.add_frame(frame, timestamp, monitor_index=0)
                if not success:
                    self.performance_stats['dropped_frames'] += 1
                    self.logger.debug("Frame dropped due to full processing queue")
            else:
                # Fallback to direct processing if thread not available
                self._process_frame_internal(frame, timestamp, monitor_index=0)

        except Exception as e:
            self.logger.error(f"Error in frame capture callback: {e}")
            self.performance_stats['dropped_frames'] += 1
    
    def _update_status_display(self, status: str):
        """Update status display in GUI components"""
        if self.system_tray:
            self.system_tray.update_status(status)
        
        if self.main_window and self.main_window.isVisible():
            self.main_window.update_status(status)
    
    def get_status(self) -> dict:
        """Get current application status"""
        capture_stats = {}
        if self.screen_capture:
            capture_stats = self.screen_capture.get_performance_stats()

        detection_stats = {}
        if self.detection_engine:
            detection_stats = self.detection_engine.get_performance_stats()

        overlay_stats = {}
        if self.overlay_renderer:
            overlay_stats = self.overlay_renderer.get_status()

        return {
            'running': self.running,
            'enabled': self.settings.enabled,
            'fps': self.current_fps,
            'capture_stats': capture_stats,
            'detection_stats': detection_stats,
            'overlay_stats': overlay_stats,
            'components': {
                'capture': self.screen_capture is not None,
                'detection': self.detection_engine is not None,
                'overlay': self.overlay_renderer is not None
            }
        }

    def get_monitors(self):
        """Get available monitors for capture"""
        if self.screen_capture:
            return self.screen_capture.get_monitors()
        return []

    def set_capture_monitor(self, monitor_index: int) -> bool:
        """Set the active monitor for capture"""
        if self.screen_capture:
            return self.screen_capture.set_monitor(monitor_index)
        return False

    def load_detection_model(self) -> bool:
        """Load the detection model"""
        if self.detection_engine:
            return self.detection_engine.load_model()
        return False

    def set_detection_confidence(self, confidence: float) -> bool:
        """Set detection confidence threshold"""
        if self.detection_engine:
            self.detection_engine.set_confidence_threshold(confidence)
            # Also update settings
            self.settings.detection.confidence_threshold = confidence
            self.settings.save()
            return True
        return False

    def _setup_overlay_monitors(self):
        """Enhanced setup overlay windows for available monitors with DPI awareness"""
        if not self.overlay_renderer:
            return

        try:
            # Get monitor information from screen capture
            if self.screen_capture:
                monitors = self.screen_capture.get_monitors()

                # Use the new enhanced setup method with MonitorInfo objects
                success = self.overlay_renderer.setup_monitors_from_monitor_info(monitors)

                if success:
                    self.logger.info(f"Setup enhanced overlays for {len(monitors)} monitors with DPI awareness")

                    # Set up monitor change callback
                    self.screen_capture.set_monitor_change_callback(self._on_monitor_configuration_changed)
                else:
                    self.logger.error("Failed to setup enhanced overlay monitors")
            else:
                self.logger.warning("Cannot setup overlay monitors: screen capture not available")

        except Exception as e:
            self.logger.error(f"Error setting up overlay monitors: {e}")

    def _on_monitor_configuration_changed(self, new_monitors):
        """Handle monitor configuration changes"""
        try:
            self.logger.info("Monitor configuration changed, updating overlays...")

            # Stop current processing temporarily
            was_running = self.get_state() == AppState.RUNNING
            if was_running:
                self.pause()

            # Re-setup overlay monitors with new configuration
            if self.overlay_renderer:
                success = self.overlay_renderer.setup_monitors_from_monitor_info(new_monitors)
                if success:
                    self.logger.info(f"Successfully updated overlays for {len(new_monitors)} monitors")
                else:
                    self.logger.error("Failed to update overlay monitors after configuration change")

            # Resume processing if it was running
            if was_running:
                self.resume()

        except Exception as e:
            self.logger.error(f"Error handling monitor configuration change: {e}")

    # Enhanced Monitor Management Methods
    def get_monitor_info(self, monitor_index: int = None):
        """Get detailed information about monitors"""
        if not self.screen_capture:
            return None

        if monitor_index is not None:
            return self.screen_capture.get_monitor_info_detailed(monitor_index)
        else:
            # Return info for all monitors
            monitors = self.screen_capture.get_monitors()
            return [self.screen_capture.get_monitor_info_detailed(i) for i in range(len(monitors))]

    def set_capture_monitor(self, monitor_index: int) -> bool:
        """Set the active monitor for capture"""
        if self.screen_capture:
            return self.screen_capture.set_monitor(monitor_index)
        return False

    def get_monitor_count(self) -> int:
        """Get the number of detected monitors"""
        if self.screen_capture:
            return self.screen_capture.get_monitor_count()
        return 0

    def refresh_monitors(self) -> bool:
        """Manually refresh monitor detection"""
        if self.screen_capture:
            success = self.screen_capture.refresh_monitors()
            if success:
                # Re-setup overlays with new monitor configuration
                self._setup_overlay_monitors()
            return success
        return False

    def set_monitor_specific_setting(self, monitor_index: int, setting_name: str, value):
        """Set monitor-specific setting for both capture and overlay"""
        if self.screen_capture:
            self.screen_capture.set_monitor_specific_setting(monitor_index, setting_name, value)

        if self.overlay_renderer:
            self.overlay_renderer.set_monitor_specific_setting(monitor_index, setting_name, value)

    def get_monitor_specific_setting(self, monitor_index: int, setting_name: str, default=None):
        """Get monitor-specific setting"""
        if self.screen_capture:
            return self.screen_capture.get_monitor_specific_setting(monitor_index, setting_name, default)
        return default

    def enable_capture_all_monitors(self, enable: bool = True):
        """Enable/disable capturing from all monitors"""
        if self.screen_capture:
            self.screen_capture.set_capture_all_monitors(enable)
            self.logger.info(f"Capture all monitors: {enable}")

    def get_primary_monitor_info(self):
        """Get information about the primary monitor"""
        if self.screen_capture:
            primary_monitor = self.screen_capture.get_primary_monitor()
            if primary_monitor:
                return self.screen_capture.get_monitor_info_detailed(primary_monitor.index)
        return None

    def get_monitor_by_point(self, x: int, y: int):
        """Get monitor containing the specified point"""
        if self.screen_capture:
            monitor = self.screen_capture.get_monitor_by_point(x, y)
            if monitor:
                return self.screen_capture.get_monitor_info_detailed(monitor.index)
        return None

    def get_total_desktop_area(self):
        """Get the total desktop area spanning all monitors"""
        if self.screen_capture:
            return self.screen_capture.get_total_desktop_area()
        return (0, 0, 1920, 1080)

    # Hotkey callback methods
    def _hotkey_toggle_censoring(self):
        """Hotkey callback: Toggle censoring on/off"""
        try:
            enabled = self.toggle_censoring()
            status = "enabled" if enabled else "disabled"
            self.logger.info(f"Hotkey: Censoring {status}")
            self.status_changed.emit(f"Censoring {status}")
        except Exception as e:
            self.logger.error(f"Error in hotkey toggle censoring: {e}")

    def _hotkey_toggle_breasts(self):
        """Hotkey callback: Toggle breast censoring"""
        try:
            enabled = self.settings.toggle_body_part(BodyPart.BREASTS)
            status = "enabled" if enabled else "disabled"
            self.logger.info(f"Hotkey: Breast censoring {status}")
            self.status_changed.emit(f"Breast censoring {status}")
        except Exception as e:
            self.logger.error(f"Error in hotkey toggle breasts: {e}")

    def _hotkey_toggle_genitals(self):
        """Hotkey callback: Toggle genital censoring"""
        try:
            enabled = self.settings.toggle_body_part(BodyPart.GENITALS)
            status = "enabled" if enabled else "disabled"
            self.logger.info(f"Hotkey: Genital censoring {status}")
            self.status_changed.emit(f"Genital censoring {status}")
        except Exception as e:
            self.logger.error(f"Error in hotkey toggle genitals: {e}")

    def _hotkey_toggle_buttocks(self):
        """Hotkey callback: Toggle buttock censoring"""
        try:
            enabled = self.settings.toggle_body_part(BodyPart.BUTTOCKS)
            status = "enabled" if enabled else "disabled"
            self.logger.info(f"Hotkey: Buttock censoring {status}")
            self.status_changed.emit(f"Buttock censoring {status}")
        except Exception as e:
            self.logger.error(f"Error in hotkey toggle buttocks: {e}")

    def _hotkey_emergency_disable(self):
        """Hotkey callback: Emergency disable all censoring"""
        try:
            # Disable all censoring immediately
            self.settings.enabled = False
            for body_part in BodyPart:
                self.settings.body_part_settings[body_part].enabled = False
            self.settings.save()

            # Stop censoring if running
            if self.running:
                self.stop_censoring()

            self.logger.info("Hotkey: Emergency disable activated")
            self.status_changed.emit("Emergency disable activated")
        except Exception as e:
            self.logger.error(f"Error in hotkey emergency disable: {e}")

    def _hotkey_preset_minimal(self):
        """Hotkey callback: Apply minimal censoring preset"""
        try:
            self._apply_preset("minimal")
            self.logger.info("Hotkey: Applied minimal preset")
            self.status_changed.emit("Applied minimal preset")
        except Exception as e:
            self.logger.error(f"Error in hotkey preset minimal: {e}")

    def _hotkey_preset_moderate(self):
        """Hotkey callback: Apply moderate censoring preset"""
        try:
            self._apply_preset("moderate")
            self.logger.info("Hotkey: Applied moderate preset")
            self.status_changed.emit("Applied moderate preset")
        except Exception as e:
            self.logger.error(f"Error in hotkey preset moderate: {e}")

    def _hotkey_preset_maximum(self):
        """Hotkey callback: Apply maximum censoring preset"""
        try:
            self._apply_preset("maximum")
            self.logger.info("Hotkey: Applied maximum preset")
            self.status_changed.emit("Applied maximum preset")
        except Exception as e:
            self.logger.error(f"Error in hotkey preset maximum: {e}")

    def _hotkey_temporary_disable(self):
        """Hotkey callback: Temporarily disable censoring"""
        try:
            duration = self.settings.hotkeys.temporary_disable_duration
            if self.hotkey_manager:
                self.hotkey_manager.start_temporary_disable(duration)

            # Also temporarily disable censoring
            was_running = self.running
            if was_running:
                self.stop_censoring()

            self.logger.info(f"Hotkey: Temporary disable for {duration} seconds")
            self.status_changed.emit(f"Temporarily disabled for {duration}s")

            # Re-enable after duration
            def re_enable():
                if was_running:
                    self.start_censoring()
                self.status_changed.emit("Temporary disable ended")

            timer = threading.Timer(duration, re_enable)
            timer.start()

        except Exception as e:
            self.logger.error(f"Error in hotkey temporary disable: {e}")

    def _hotkey_stealth_mode(self):
        """Hotkey callback: Toggle stealth mode"""
        try:
            if self.hotkey_manager:
                self.hotkey_manager.toggle_stealth_mode()
                status = "activated" if self.hotkey_manager.stealth_mode_active else "deactivated"
                self.logger.info(f"Hotkey: Stealth mode {status}")
                self.status_changed.emit(f"Stealth mode {status}")
        except Exception as e:
            self.logger.error(f"Error in hotkey stealth mode: {e}")

    def _hotkey_increase_intensity(self):
        """Hotkey callback: Increase censoring intensity"""
        try:
            self._adjust_intensity(0.1)  # Increase by 10%
            self.logger.info("Hotkey: Increased censoring intensity")
            self.status_changed.emit("Increased intensity")
        except Exception as e:
            self.logger.error(f"Error in hotkey increase intensity: {e}")

    def _hotkey_decrease_intensity(self):
        """Hotkey callback: Decrease censoring intensity"""
        try:
            self._adjust_intensity(-0.1)  # Decrease by 10%
            self.logger.info("Hotkey: Decreased censoring intensity")
            self.status_changed.emit("Decreased intensity")
        except Exception as e:
            self.logger.error(f"Error in hotkey decrease intensity: {e}")

    def _apply_preset(self, preset_name: str):
        """Apply a censoring preset"""
        presets = {
            "minimal": {
                "method": CensorMethod.BLUR_LIGHT,
                "intensity": 0.3,
                "opacity": 0.5
            },
            "moderate": {
                "method": CensorMethod.BLUR,
                "intensity": 0.7,
                "opacity": 0.8
            },
            "maximum": {
                "method": CensorMethod.BLACK_BAR,
                "intensity": 1.0,
                "opacity": 1.0
            }
        }

        if preset_name not in presets:
            self.logger.warning(f"Unknown preset: {preset_name}")
            return

        preset = presets[preset_name]

        # Apply preset to all body parts
        for body_part in BodyPart:
            settings = self.settings.get_body_part_setting(body_part)
            settings.method = preset["method"]
            settings.intensity = preset["intensity"]
            settings.opacity = preset["opacity"]
            settings.enabled = True
            self.settings.set_body_part_setting(body_part, settings)

        # Enable overall censoring
        self.settings.enabled = True
        self.settings.save()

    def _adjust_intensity(self, delta: float):
        """Adjust intensity for all enabled body parts"""
        for body_part in BodyPart:
            settings = self.settings.get_body_part_setting(body_part)
            if settings.enabled:
                # Clamp intensity between 0.0 and 1.0
                new_intensity = max(0.0, min(1.0, settings.intensity + delta))
                settings.intensity = new_intensity
                self.settings.set_body_part_setting(body_part, settings)

        self.settings.save()

    def get_performance_stats(self) -> Dict[str, Any]:
        """Get current performance statistics"""
        stats = self.performance_stats.copy()

        # Add performance manager metrics if available
        if self.performance_manager:
            pm_metrics = self.performance_manager.get_current_metrics()
            stats.update({
                'cpu_usage': pm_metrics.cpu_usage,
                'memory_usage': pm_metrics.memory_usage,
                'memory_usage_mb': pm_metrics.memory_usage_mb,
                'cache_hits': pm_metrics.cache_hits,
                'cache_misses': pm_metrics.cache_misses,
                'current_quality_level': pm_metrics.current_quality_level,
                'roi_regions': len(self.performance_manager.roi_regions) if self.performance_manager.roi_regions else 0
            })

        return stats

    def cleanup(self):
        """Clean up resources"""
        try:
            # Stop censoring
            self.stop_censoring()

            # Clean up hotkey manager
            if self.hotkey_manager:
                self.hotkey_manager.cleanup()

            # Clean up other components
            if self.processing_thread and self.processing_thread.isRunning():
                self.processing_thread.stop()
                self.processing_thread.wait()

            if self.overlay_renderer:
                self.overlay_renderer.cleanup()

            if self.performance_manager:
                self.performance_manager.cleanup()

            self.logger.info("AppController cleanup completed")

        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")
