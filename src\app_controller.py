"""
Main Application Controller for Censor App
Coordinates all components and manages the application lifecycle
"""

import logging
import threading
import time
import queue
import sys
import os
from typing import Optional, Dict, Any
from enum import Enum
from PyQt6.QtCore import QObject, QTimer, pyqtSignal, QThread, QMutex, QWaitCondition
from PyQt6.QtWidgets import QApplication

from src.config.settings import Settings, BodyPart
from src.gui.system_tray import SystemTray
from src.gui.main_window import MainWindow
from src.capture.screen_capture import ScreenCapture
from src.detection.nudity_detector import NudityDetector
from src.overlay.overlay_renderer import OverlayRenderer


class AppState(Enum):
    """Application state enumeration"""
    STOPPED = "stopped"
    STARTING = "starting"
    RUNNING = "running"
    STOPPING = "stopping"
    ERROR = "error"


class ProcessingThread(QThread):
    """Dedicated thread for frame processing"""

    # Signals for thread communication
    frame_processed = pyqtSignal(object)  # Processed frame data
    error_occurred = pyqtSignal(str)      # Error message
    stats_updated = pyqtSignal(dict)      # Performance statistics

    def __init__(self, controller):
        super().__init__()
        self.controller = controller
        self.logger = logging.getLogger(__name__)

        # Thread control
        self.running = False
        self.mutex = QMutex()
        self.wait_condition = QWaitCondition()

        # Frame queue for processing
        self.frame_queue = queue.Queue(maxsize=10)  # Limit queue size

        # Performance tracking
        self.frames_processed = 0
        self.processing_times = []
        self.last_stats_time = time.time()

    def run(self):
        """Main thread processing loop"""
        self.logger.info("Processing thread started")
        self.running = True

        while self.running:
            try:
                # Get frame from queue (with timeout)
                try:
                    frame_data = self.frame_queue.get(timeout=0.1)
                except queue.Empty:
                    continue

                # Process frame
                start_time = time.time()
                self._process_frame(frame_data)
                processing_time = time.time() - start_time

                # Update performance stats
                self.frames_processed += 1
                self.processing_times.append(processing_time)

                # Emit stats periodically
                if time.time() - self.last_stats_time > 1.0:
                    self._emit_stats()
                    self.last_stats_time = time.time()

                # Mark frame as processed
                self.frame_queue.task_done()

            except Exception as e:
                self.logger.error(f"Error in processing thread: {e}")
                self.error_occurred.emit(str(e))

        self.logger.info("Processing thread stopped")

    def _process_frame(self, frame_data):
        """Process a single frame"""
        frame, timestamp, monitor_index = frame_data

        # Delegate to controller for actual processing
        if self.controller and hasattr(self.controller, '_process_frame_internal'):
            result = self.controller._process_frame_internal(frame, timestamp, monitor_index)
            self.frame_processed.emit(result)

    def _emit_stats(self):
        """Emit performance statistics"""
        if not self.processing_times:
            return

        avg_time = sum(self.processing_times) / len(self.processing_times)
        max_time = max(self.processing_times)
        fps = 1.0 / avg_time if avg_time > 0 else 0

        stats = {
            'frames_processed': self.frames_processed,
            'avg_processing_time': avg_time,
            'max_processing_time': max_time,
            'processing_fps': fps,
            'queue_size': self.frame_queue.qsize()
        }

        self.stats_updated.emit(stats)

        # Keep only recent processing times
        if len(self.processing_times) > 100:
            self.processing_times = self.processing_times[-50:]

    def add_frame(self, frame, timestamp, monitor_index=0):
        """Add frame to processing queue"""
        if not self.running:
            return False

        try:
            # Add frame to queue (non-blocking)
            self.frame_queue.put_nowait((frame, timestamp, monitor_index))
            return True
        except queue.Full:
            # Queue is full, drop frame
            self.logger.warning("Processing queue full, dropping frame")
            return False

    def stop_processing(self):
        """Stop the processing thread"""
        self.running = False
        self.wait()  # Wait for thread to finish


class AppController(QObject):
    """Main application controller that coordinates all components"""
    
    # Signals
    detection_result = pyqtSignal(dict)  # Emitted when detection results are available
    status_changed = pyqtSignal(str)     # Emitted when app status changes
    state_changed = pyqtSignal(str)      # Application state changes
    error_occurred = pyqtSignal(str)     # Error notifications

    def __init__(self, settings: Settings, headless: bool = False):
        super().__init__()
        self.settings = settings
        self.headless = headless
        self.logger = logging.getLogger(__name__)

        # Application state management
        self.app_state = AppState.STOPPED
        self.state_lock = threading.Lock()
        self.startup_error = None

        # Component references
        self.system_tray: Optional[SystemTray] = None
        self.main_window: Optional[MainWindow] = None

        # Core components
        self.screen_capture: Optional[ScreenCapture] = None
        self.detection_engine: Optional[NudityDetector] = None
        self.overlay_renderer: Optional[OverlayRenderer] = None

        # Enhanced threading and control
        self.running = False
        self.main_thread: Optional[threading.Thread] = None
        self.processing_thread: Optional[ProcessingThread] = None
        self.processing_timer: Optional[QTimer] = None

        # Enhanced performance tracking
        self.fps_counter = 0
        self.last_fps_time = time.time()
        self.current_fps = 0
        self.performance_stats = {
            'capture_fps': 0.0,
            'processing_fps': 0.0,
            'detection_time': 0.0,
            'overlay_time': 0.0,
            'total_frames': 0,
            'dropped_frames': 0,
            'queue_size': 0
        }
        
        self._setup_components()
    
    def _setup_components(self):
        """Initialize all application components"""
        try:
            self._set_state(AppState.STARTING)

            # Initialize GUI components (unless headless)
            if not self.headless:
                self.system_tray = SystemTray(self)
                self.main_window = MainWindow(self)
                self.logger.info("GUI components initialized")

            # Initialize screen capture
            self.screen_capture = ScreenCapture(callback=self._on_frame_captured)
            self.screen_capture.set_target_fps(self.settings.performance.max_fps)
            max_res = self.settings.performance.max_resolution
            self.screen_capture.set_max_resolution(max_res[0], max_res[1])
            self.logger.info("Screen capture initialized")

            # Initialize detection engine
            self.detection_engine = NudityDetector()
            self.detection_engine.set_confidence_threshold(self.settings.detection.confidence_threshold)
            # Use default detection resolution of 416x416 (common for YOLO models)
            detection_resolution = getattr(self.settings.detection, 'input_size', (416, 416))
            self.detection_engine.set_input_size(*detection_resolution)
            self.logger.info("Detection engine initialized")

            # Initialize overlay renderer
            self.overlay_renderer = OverlayRenderer()
            self._setup_overlay_monitors()
            self.logger.info("Overlay renderer initialized")

            # Initialize processing thread
            self.processing_thread = ProcessingThread(self)
            self.processing_thread.frame_processed.connect(self._on_frame_processed)
            self.processing_thread.error_occurred.connect(self._on_processing_error)
            self.processing_thread.stats_updated.connect(self._on_processing_stats)
            self.logger.info("Processing thread initialized")

            self._set_state(AppState.STOPPED)
            self.logger.info("All components initialized successfully")

            # Connect signals
            self._connect_signals()

            self.logger.info("Application components initialized successfully")

        except Exception as e:
            self.logger.error(f"Error setting up components: {e}")
            raise
    
    def _connect_signals(self):
        """Connect signals between components"""
        # Connect system tray signals
        if self.system_tray:
            self.system_tray.toggle_requested.connect(self.toggle_censoring)
            self.system_tray.settings_requested.connect(self.show_settings)
            self.system_tray.quit_requested.connect(self.stop)
        
        # Connect main window signals
        if self.main_window:
            self.main_window.settings_changed.connect(self.on_settings_changed)
        
        # Connect status updates
        self.status_changed.connect(self._update_status_display)
    
    def start(self):
        """Start the application"""
        try:
            self.logger.info("Starting Censor App...")
            
            # Show system tray
            if self.system_tray:
                self.system_tray.show()
            
            # Initialize processing timer for future use
            self.processing_timer = QTimer()
            self.processing_timer.timeout.connect(self._process_frame)
            
            # Start censoring if enabled in settings
            if self.settings.enabled:
                self.start_censoring()
            
            self.status_changed.emit("Application started")
            self.logger.info("Censor App started successfully")
            
        except Exception as e:
            self.logger.error(f"Error starting application: {e}")
            raise
    
    def stop(self):
        """Stop the application"""
        try:
            self.logger.info("Stopping Censor App...")
            
            # Stop censoring
            self.stop_censoring()
            
            # Hide system tray
            if self.system_tray:
                self.system_tray.hide()
            
            # Close main window
            if self.main_window:
                self.main_window.close()
            
            # Quit application
            QApplication.quit()
            
            self.status_changed.emit("Application stopped")
            self.logger.info("Censor App stopped successfully")
            
        except Exception as e:
            self.logger.error(f"Error stopping application: {e}")

    def _set_state(self, new_state: AppState):
        """Thread-safe state management"""
        with self.state_lock:
            if self.app_state != new_state:
                old_state = self.app_state
                self.app_state = new_state
                self.logger.info(f"State changed: {old_state.value} -> {new_state.value}")
                self.state_changed.emit(new_state.value)

    def get_state(self) -> AppState:
        """Get current application state"""
        with self.state_lock:
            return self.app_state

    def start_censoring(self):
        """Start the censoring process"""
        if self.running:
            return

        try:
            self.logger.info("Starting censoring process...")
            self.running = True

            # Start screen capture
            if self.screen_capture:
                capture_started = self.screen_capture.start_capture()
                if not capture_started:
                    self.logger.error("Failed to start screen capture")
                    self.running = False
                    return

            # Start processing timer for performance monitoring
            if self.processing_timer:
                self.processing_timer.start(1000)  # Update stats every second

            self.status_changed.emit("Censoring active")
            self.logger.info("Censoring process started")

        except Exception as e:
            self.logger.error(f"Error starting censoring: {e}")
            self.running = False
    
    def stop_censoring(self):
        """Stop the censoring process"""
        if not self.running:
            return

        try:
            self.logger.info("Stopping censoring process...")
            self.running = False

            # Stop screen capture
            if self.screen_capture:
                self.screen_capture.stop_capture()

            # Stop processing timer
            if self.processing_timer:
                self.processing_timer.stop()

            self.status_changed.emit("Censoring inactive")
            self.logger.info("Censoring process stopped")

        except Exception as e:
            self.logger.error(f"Error stopping censoring: {e}")

    def _process_frame_internal(self, frame, timestamp, monitor_index=0):
        """Internal frame processing method for threading"""
        try:
            start_time = time.time()

            # Update frame count and FPS
            self.fps_counter += 1
            current_time = time.time()
            if current_time - self.last_fps_time >= 1.0:
                self.current_fps = self.fps_counter / (current_time - self.last_fps_time)
                self.fps_counter = 0
                self.last_fps_time = current_time
                self.performance_stats['capture_fps'] = self.current_fps

            # Skip processing if not enabled
            if not self.settings.enabled:
                return None

            detection_start = time.time()
            detection_result = None
            filtered_detections = []

            # Run detection if engine is available
            if self.detection_engine:
                detection_result = self.detection_engine.detect_nudity(frame)
                enabled_parts = self.settings.get_enabled_body_parts()
                filtered_detections = self.detection_engine.filter_detections_by_body_parts(
                    detection_result.detections, enabled_parts
                )

            detection_time = time.time() - detection_start
            self.performance_stats['detection_time'] = detection_time

            # Apply overlay rendering
            overlay_start = time.time()
            if filtered_detections and self.overlay_renderer:
                # Get censor settings for each body part
                censor_settings = {
                    part: self.settings.get_body_part_setting(part)
                    for part in [BodyPart.BREASTS, BodyPart.GENITALS, BodyPart.BUTTOCKS]
                }

                # Update overlays
                self.overlay_renderer.update_overlays(
                    filtered_detections,
                    monitor_index=monitor_index,
                    frame_shape=frame.shape[:2],
                    censor_settings=censor_settings
                )

                # Show overlays if not visible
                if not self.overlay_renderer.visible:
                    self.overlay_renderer.show_overlays()

            elif self.overlay_renderer and self.overlay_renderer.visible:
                # Hide overlays if no detections
                self.overlay_renderer.hide_overlays()

            overlay_time = time.time() - overlay_start
            self.performance_stats['overlay_time'] = overlay_time
            self.performance_stats['total_frames'] += 1

            # Emit detection result signal
            if detection_result:
                self.detection_result.emit({
                    'detections': len(filtered_detections),
                    'timestamp': timestamp,
                    'monitor': monitor_index,
                    'processing_time': time.time() - start_time
                })

            return {
                'detections': filtered_detections,
                'processing_time': time.time() - start_time,
                'timestamp': timestamp
            }

        except Exception as e:
            self.logger.error(f"Error processing frame: {e}")
            self.performance_stats['dropped_frames'] += 1
            return None

    def _on_frame_processed(self, result):
        """Handle processed frame results from processing thread"""
        if result:
            # Update performance stats or handle results as needed
            pass

    def _on_processing_error(self, error_msg):
        """Handle processing thread errors"""
        self.logger.error(f"Processing thread error: {error_msg}")
        self.error_occurred.emit(error_msg)

    def _on_processing_stats(self, stats):
        """Handle processing thread statistics"""
        self.performance_stats.update(stats)

    def toggle_censoring(self):
        """Toggle censoring on/off"""
        if self.running:
            self.stop_censoring()
            self.settings.enabled = False
        else:
            self.start_censoring()
            self.settings.enabled = True
        
        self.settings.save()
        return self.running
    
    def show_settings(self):
        """Show the settings window"""
        if self.main_window:
            self.main_window.show()
            self.main_window.raise_()
            self.main_window.activateWindow()
    
    def on_settings_changed(self):
        """Handle settings changes"""
        self.logger.info("Settings changed, applying updates...")
        
        # Restart censoring if it was running to apply new settings
        if self.running:
            self.stop_censoring()
            self.start_censoring()
    
    def _process_frame(self):
        """Process performance monitoring and stats update"""
        if self.screen_capture and self.running:
            # Get capture performance stats
            stats = self.screen_capture.get_performance_stats()
            self.current_fps = stats.get('fps', 0)

            # Log performance occasionally
            if self.current_fps > 0:
                self.logger.debug(f"Capture FPS: {self.current_fps}, Target: {stats.get('target_fps', 0)}")

    def _on_frame_captured(self, frame):
        """Enhanced callback for when a frame is captured - uses threading"""
        try:
            # Count frames for performance tracking
            self.fps_counter += 1
            timestamp = time.time()

            # Skip processing if censoring is disabled
            if not self.settings.enabled:
                return

            # Add frame to processing queue if thread is available
            if self.processing_thread and self.processing_thread.isRunning():
                success = self.processing_thread.add_frame(frame, timestamp, monitor_index=0)
                if not success:
                    self.performance_stats['dropped_frames'] += 1
                    self.logger.debug("Frame dropped due to full processing queue")
            else:
                # Fallback to direct processing if thread not available
                self._process_frame_internal(frame, timestamp, monitor_index=0)

        except Exception as e:
            self.logger.error(f"Error in frame capture callback: {e}")
            self.performance_stats['dropped_frames'] += 1
    
    def _update_status_display(self, status: str):
        """Update status display in GUI components"""
        if self.system_tray:
            self.system_tray.update_status(status)
        
        if self.main_window and self.main_window.isVisible():
            self.main_window.update_status(status)
    
    def get_status(self) -> dict:
        """Get current application status"""
        capture_stats = {}
        if self.screen_capture:
            capture_stats = self.screen_capture.get_performance_stats()

        detection_stats = {}
        if self.detection_engine:
            detection_stats = self.detection_engine.get_performance_stats()

        overlay_stats = {}
        if self.overlay_renderer:
            overlay_stats = self.overlay_renderer.get_status()

        return {
            'running': self.running,
            'enabled': self.settings.enabled,
            'fps': self.current_fps,
            'capture_stats': capture_stats,
            'detection_stats': detection_stats,
            'overlay_stats': overlay_stats,
            'components': {
                'capture': self.screen_capture is not None,
                'detection': self.detection_engine is not None,
                'overlay': self.overlay_renderer is not None
            }
        }

    def get_monitors(self):
        """Get available monitors for capture"""
        if self.screen_capture:
            return self.screen_capture.get_monitors()
        return []

    def set_capture_monitor(self, monitor_index: int) -> bool:
        """Set the active monitor for capture"""
        if self.screen_capture:
            return self.screen_capture.set_monitor(monitor_index)
        return False

    def load_detection_model(self) -> bool:
        """Load the detection model"""
        if self.detection_engine:
            return self.detection_engine.load_model()
        return False

    def set_detection_confidence(self, confidence: float) -> bool:
        """Set detection confidence threshold"""
        if self.detection_engine:
            self.detection_engine.set_confidence_threshold(confidence)
            # Also update settings
            self.settings.detection.confidence_threshold = confidence
            self.settings.save()
            return True
        return False

    def _setup_overlay_monitors(self):
        """Setup overlay windows for available monitors"""
        if not self.overlay_renderer:
            return

        try:
            # Get monitor information from screen capture
            if self.screen_capture:
                monitors = self.screen_capture.get_monitors()

                # Convert to QRect format for overlay renderer
                from PyQt6.QtCore import QRect
                monitor_geometries = []

                for monitor in monitors:
                    geometry = QRect(
                        monitor.x, monitor.y,
                        monitor.width, monitor.height
                    )
                    monitor_geometries.append(geometry)

                # Setup overlay renderer with monitor geometries
                self.overlay_renderer.setup_monitors(monitor_geometries)
                self.logger.info(f"Setup overlays for {len(monitor_geometries)} monitors")
            else:
                self.logger.warning("Cannot setup overlay monitors: screen capture not available")

        except Exception as e:
            self.logger.error(f"Error setting up overlay monitors: {e}")
